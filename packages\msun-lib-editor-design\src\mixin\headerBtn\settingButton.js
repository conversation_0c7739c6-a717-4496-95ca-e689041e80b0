const settingBtnMixIn = {
  data() {
    return {
      pageSizeList: ["A4", "A5", "A6", "B5", "B5(JIS)", "16K", "16K(BIG)"],
      pageDirection: "vertical",
      pageSize: "A4",
      settingBtn: [
        {
          type: "groupMenu",
          icon: "icon-zhizhangdaxiao",
          title: "纸张大小",
          showType: "pageSize",
          children: [
            {
              type: "textIcon",
              icon: "icon-zhizhangdaxiao",
              title: "A4",
              func: () => {
                this.changePageSize("A4");
              },
            },
            {
              type: "textIcon",
              icon: "icon-zhizhangdaxiao",
              title: "A5",
              func: () => {
                this.changePageSize("A5");
              },
            },
            {
              type: "textIcon",
              icon: "icon-zhizhangdaxiao",
              title: "A6",
              func: () => {
                this.changePageSize("A6");
              },
            },
            {
              type: "textIcon",
              icon: "icon-zhizhangdaxiao",
              title: "B5",
              func: () => {
                this.changePageSize("B5");
              },
            },
            {
              type: "textIcon",
              icon: "icon-zhizhangdaxiao",
              title: "B5(JIS)",
              func: () => {
                this.changePageSize("B5(JIS)");
              },
            },
            {
              type: "textIcon",
              icon: "icon-zhizhangdaxiao",
              title: "16K",
              func: () => {
                this.changePageSize("16K");
              },
            },
            {
              type: "textIcon",
              icon: "icon-zhizhangdaxiao",
              title: "16K(BIG)",
              func: () => {
                this.changePageSize("16K(BIG)");
              },
            },
          ],
        },
        {
          type: "groupMenu",
          icon: "icon-yemianfangxiang",
          title: "页面方向",
          showType: "pageDirection",
          children: [
            {
              type: "textIcon",
              icon: "icon-zongxiang",
              title: "纵向",
              key: "page_direction",
              val: "vertical",
              func: () => {
                this.changeDirection("vertical");
              },
            },
            {
              type: "textIcon",
              icon: "icon-hengxiang",
              title: "横向",
              key: "page_direction",
              val: "horizontal",
              func: () => {
                this.changeDirection("horizontal");
              },
            },
          ],
        },
        {
          type: "groupMenu",
          icon: "icon-yemeiyejiao",
          title: "页眉页脚横线",
          showType: "headerFooterLine",
          children: [
            {
              type: "textIcon",
              icon: "icon-fengexiantiao",
              title: "页眉线显示控制",
              func: this.headerLineSet,
            },
            {
              type: "textIcon",
              icon: "icon-fengexiantiao",
              title: "页脚线显示控制",
              func: this.footerLineSet,
            },
          ],
        },
        {
          type: "textIcon",
          icon: "icon-yemianshezhi",
          title: "页面设置",
          func: this.pageSetting,
        },
        {
          type: "textIcon",
          icon: "icon-yemianshezhi",
          title: "打印设置",
          func: this.showImmediatelyPrintConfig,
        },

        // {
        //   type: "textIcon",
        //   icon: "icon-shitu",
        //   title: "只读模式",
        //   key: "readonly",
        //   val: true,
        //   func: this.onlyReadPattern,
        // },
      ],
    };
  },
  created() {},
  methods: {
    // 水平线显示控制
    headerLineSet() {
      const config = this.instance.config.getConfig();
      // 当前页眉水平线显示状态
      const headerLine = config.show_header_line;
      // 当前页脚水平线显示状态
      const footerLine = config.show_footer_line;
      this.instance.editor.headerFooterHorizontal(!headerLine, footerLine);
    },
    // 水平线显示控制
    footerLineSet() {
      const config = this.instance.config.getConfig();
      // 当前页眉水平线显示状态
      const headerLine = config.show_header_line;
      // 当前页脚水平线显示状态
      const footerLine = config.show_footer_line;
      this.instance.editor.headerFooterHorizontal(headerLine, !footerLine);
    },
    // 修改页面纸张大小
    changePageStyle() {
      this.instance.editor.changePageSize(this.pageSize, this.pageDirection);
    },
    changePageSize(val) {
      this.pageSize = val;
      this.changePageStyle();
    },
    changeDirection(val) {
      this.pageDirection = val;
      this.changePageStyle();
    },

    pageSetting() {
      this.instance.openPageConfigModal();
    },
    showImmediatelyPrintConfig() {
      this.instance.showImmediatelyPrintConfigModal();
    },
  },
};
export default settingBtnMixIn;
