// eslint-disable-next-line @typescript-eslint/no-unused-vars
function action<PERSON><PERSON><PERSON> (Editor) {
  const editor = Editor.editor;
  const img_src = "https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fimg2.baidu.com%2Fit%2Fu%3D98371021%2C1121096365%26fm%3D253%26app%3D120%26f%3DJPEG%3Fw%3D1200%26h%3D800&refer=http%3A%2F%2Fimg2.baidu.com&app=2002&size=w300&q=a80&n=0&g=0n&fmt=jpeg?sec=1645582607&t=cc63571e03f0e52e3493f1edb6e47b57";
  return [
    {
      title: "测试",
      children: [
        {
          title: "万能按钮",
          exe (e) {
            e.preventDefault();
            const field = editor.getFieldById("t1");
            const data = field.getDescription();
            console.log(data, "描述信息");
            // editor.intertWarterMarkBy({});
            // const bodyText = editor.getBodyText();
            // console.log(bodyText, "打印");
            // const fields = editor.getAllFields();
            // for (let i = 0; i < fields.length; i++) {
            //   if (i === 0) {
            //     fields[i].defaultValue = "第一个哦";
            //   } else if (i === fields.length - 1) {
            //     fields[i].defaultValue = "这是最后一个";
            //   } else {
            //     fields[i].defaultValue = "中间过程";
            //   }
            // }
            // editor.updateFieldsTextByDefaultValue({ fields });
            // editor.reviseFieldAttr({
            //   field: editor.getAllFields()[0],
            //   placeholder: "asdgasdgasdgasdgasdgdg"
            // });
            // const field = editor.getAllFields()[0];
            // field.setNewText("123123123123123");
            // editor.updateFieldText({ fields: [field] });
            // editor.refreshDocument();
            // const table1 = editor.current_cell.children[8];
            // table1.setColWith([
            //   14,
            //   129,
            //   39,
            //   71,
            //   40,
            //   111,
            //   63,
            //   39,
            //   112,
            //   99
            // ]);
            // const table2 = editor.current_cell.children[0];
            // table2.setOpacityOfCellLines([{
            //   cell: table2.children[4],
            //   top: 0
            // }]);
            // const cell = editor.current_cell.children[4].children[1];
            // const rawData = cell.getContentRawData();
            // console.log(rawData, "打印单元格");
            // const fields = editor.getFieldsByName("B0105");
            // for (let i = 0; i < fields.length; i++) {
            //   const field = fields[i]?.getOutermostParent();
            //   field.deleteSentenceWithKeywords([]);
            // }
            // editor.toggleRowLineType(2);
            // const data = localStorage.getItem("rawData11");
            // editor.reInitRaw(JSON.parse(data));
            // editor.refreshDocument();
            // const res = editor.getFieldContentByName(["aa", "bb"], false, false);
            // console.log(res);

            // // 自定义多选框 设置其中某项选中 ↓
            // const fields = editor.getFieldsByName("ceshi")[0].box_children;
            // console.log(fields, "打印");
            // fields[0].updateBoxChecked(true);
            // fields[2].updateBoxChecked(true);
            // editor.refreshDocument();
            // // // 自定义多选框 设置其中某项选中 ↑

            // const field = editor.getFieldsByName("ceshi")[0];
            // field.updateGroupCheckedByValue("v4");
            // const field = editor.getFieldsByName("B0105")[0];
            // const description = field.getContentDescription();
            // console.log(description, "描述");

            // const str = "测试";
            // const s = JSON.stringify(str);
            // const s1 = JSON.stringify(s);
            // const s2 = JSON.stringify(s1);
            // const s3 = JSON.stringify(s2).replace(/\\"/g, '"');
            // console.log(s3, "打印 s3");
            // const n = JSON.parse(s3);
            // console.log(n, "打印最终的");
            // editor.setViewMode("form");
            // const field = editor.getFieldsByName("clinicalRecordCode")[0];
            // editor.refreshDocument(true);
            // field.setNewText("666");
            // editor.updateFieldText({ fields: [field], append: false });

            // const templateData = {
            //   header: [],
            //   footer: [],
            //   content: [],
            //   groups: [],
            //   fontMap: {
            //     "font-24cb12b4": {
            //       id: "font-24cb12b4",
            //       height: 21.5,
            //       family: "宋体",
            //       bold: false,
            //       italic: false,
            //       underline: false,
            //       dblUnderLine: false,
            //       strikethrough: false,
            //       script: 3,
            //       color: "blue",
            //       bgColor: null
            //     },
            //     "font-88b16d6c": {
            //       id: "font-88b16d6c",
            //       height: 21.5,
            //       family: "宋体",
            //       bold: false,
            //       italic: false,
            //       underline: false,
            //       dblUnderLine: false,
            //       strikethrough: false,
            //       script: 3,
            //       color: "#000",
            //       bgColor: null
            //     },
            //     "font-3371eb6b": {
            //       id: "font-3371eb6b",
            //       height: 19,
            //       family: "宋体",
            //       bold: false,
            //       italic: false,
            //       underline: false,
            //       dblUnderLine: false,
            //       strikethrough: false,
            //       script: 3,
            //       color: "blue",
            //       bgColor: null
            //     },
            //     "font-53f08810": {
            //       id: "font-53f08810",
            //       height: 19,
            //       family: "宋体",
            //       bold: false,
            //       italic: false,
            //       underline: false,
            //       dblUnderLine: false,
            //       strikethrough: false,
            //       script: 3,
            //       color: "#000",
            //       bgColor: null
            //     },
            //     "font-95009e76": {
            //       id: "font-95009e76",
            //       height: 16,
            //       family: "宋体",
            //       bold: false,
            //       italic: false,
            //       underline: false,
            //       dblUnderLine: false,
            //       strikethrough: false,
            //       script: 3,
            //       color: "blue",
            //       bgColor: null
            //     },
            //     "font-default-18826904": {
            //       id: "font-default-18826904",
            //       height: 16,
            //       family: "宋体",
            //       bold: false,
            //       italic: false,
            //       underline: false,
            //       dblUnderLine: false,
            //       strikethrough: false,
            //       script: 3,
            //       color: "#000",
            //       bgColor: null
            //     },
            //     "font-238a7475": {
            //       id: "font-238a7475",
            //       height: 16,
            //       family: "宋体",
            //       bold: false,
            //       italic: false,
            //       underline: false,
            //       dblUnderLine: false,
            //       strikethrough: false,
            //       script: 3,
            //       color: "black",
            //       bgColor: null
            //     },
            //     "font-a63d0ef2": {
            //       id: "font-a63d0ef2",
            //       height: 15,
            //       family: "仿宋",
            //       bold: false,
            //       italic: false,
            //       underline: false,
            //       dblUnderLine: false,
            //       strikethrough: false,
            //       script: 3,
            //       color: "blue",
            //       bgColor: null
            //     },
            //     "font-342713ff": {
            //       id: "font-342713ff",
            //       height: 15,
            //       family: "仿宋",
            //       bold: false,
            //       italic: false,
            //       underline: false,
            //       dblUnderLine: false,
            //       strikethrough: false,
            //       script: 3,
            //       color: "#000",
            //       bgColor: null
            //     }
            //   },
            //   bodyText: "",
            //   config: {
            //   },
            //   meta: {
            //   }
            // };

            // const params = [
            //   {
            //     type: "rawData",
            //     value: {
            //       header: [],
            //       footer: [],
            //       content: [
            //         {
            //           id: "para-26b179c2",
            //           type: "p",
            //           align: "left",
            //           deepNum: 0,
            //           islist: false,
            //           isOrder: false,
            //           indentation: 0,
            //           dispersed_align: false,
            //           before_paragraph_spacing: 0,
            //           row_ratio: 1.6,
            //           page_break: false,
            //           children: [
            //             {
            //               type: "text",
            //               value: "T：",
            //               font_id: "font-default-18826904"
            //             },
            //             {
            //               type: "field",
            //               id: "field35",
            //               field_type: "select",
            //               start_symbol: "(",
            //               end_symbol: ")",
            //               readonly: 0,
            //               deletable: 1,
            //               placeholder: "",
            //               tip: "",
            //               source_id: "",
            //               source_list: [
            //                 {
            //                   text: "35.5"
            //                 },
            //                 {
            //                   text: "35.6"
            //                 },
            //                 {
            //                   text: "35.7"
            //                 },
            //                 {
            //                   text: "35.8"
            //                 },
            //                 {
            //                   text: "35.9"
            //                 },
            //                 {
            //                   text: "36.0"
            //                 },
            //                 {
            //                   text: "36.1"
            //                 },
            //                 {
            //                   text: "36.2"
            //                 },
            //                 {
            //                   text: "36.3"
            //                 },
            //                 {
            //                   text: "36.4"
            //                 },
            //                 {
            //                   text: "36.5"
            //                 },
            //                 {
            //                   text: "36.6"
            //                 },
            //                 {
            //                   text: "36.7"
            //                 },
            //                 {
            //                   text: "36.8"
            //                 },
            //                 {
            //                   text: "36.9"
            //                 },
            //                 {
            //                   text: "37.0"
            //                 },
            //                 {
            //                   text: "37.1"
            //                 },
            //                 {
            //                   text: "37.2"
            //                 },
            //                 {
            //                   text: "37.3"
            //                 },
            //                 {
            //                   text: "37.4"
            //                 },
            //                 {
            //                   text: "37.5"
            //                 },
            //                 {
            //                   text: "37.6"
            //                 },
            //                 {
            //                   text: "37.7"
            //                 },
            //                 {
            //                   text: "37.8"
            //                 },
            //                 {
            //                   text: "37.9"
            //                 },
            //                 {
            //                   text: "38.0"
            //                 },
            //                 {
            //                   text: "38.1"
            //                 },
            //                 {
            //                   text: "38.2"
            //                 },
            //                 {
            //                   text: "38.3"
            //                 },
            //                 {
            //                   text: "38.4"
            //                 },
            //                 {
            //                   text: "38.5"
            //                 },
            //                 {
            //                   text: "38.6"
            //                 },
            //                 {
            //                   text: "38.7"
            //                 },
            //                 {
            //                   text: "38.8"
            //                 },
            //                 {
            //                   text: "38.9"
            //                 },
            //                 {
            //                   text: "39.0"
            //                 },
            //                 {
            //                   text: "39.1"
            //                 },
            //                 {
            //                   text: "39.2"
            //                 },
            //                 {
            //                   text: "39.3"
            //                 },
            //                 {
            //                   text: "39.4"
            //                 },
            //                 {
            //                   text: "39.5"
            //                 },
            //                 {
            //                   text: "39.6"
            //                 },
            //                 {
            //                   text: "39.7"
            //                 },
            //                 {
            //                   text: "39.8"
            //                 },
            //                 {
            //                   text: "39.9"
            //                 },
            //                 {
            //                   text: "40.0"
            //                 },
            //                 {
            //                   text: "40.1"
            //                 },
            //                 {
            //                   text: "40.2"
            //                 },
            //                 {
            //                   text: "40.3"
            //                 },
            //                 {
            //                   text: "40.4"
            //                 },
            //                 {
            //                   text: "40.5"
            //                 },
            //                 {
            //                   text: "40.6"
            //                 },
            //                 {
            //                   text: "40.7"
            //                 },
            //                 {
            //                   text: "40.8"
            //                 },
            //                 {
            //                   text: "40.9"
            //                 },
            //                 {
            //                   text: "41.0"
            //                 }
            //               ],
            //               show_format: "",
            //               replace_format: "",
            //               meta: {},
            //               active_type: 0,
            //               multi_select: 0,
            //               max_width: 0,
            //               min_width: 0,
            //               style: {
            //                 id: "font-e3b2b8cd",
            //                 height: 16,
            //                 family: "宋体",
            //                 bold: false,
            //                 italic: false,
            //                 underline: false,
            //                 dblUnderLine: false,
            //                 strikethrough: false,
            //                 script: 3,
            //                 color: "#000",
            //                 bgColor: null
            //               },
            //               valid: 0,
            //               valid_content: "",
            //               children: [
            //                 {
            //                   type: "text",
            //                   value: "",
            //                   font_id: "font-95009e76"
            //                 },
            //                 {
            //                   type: "text",
            //                   value: "体温",
            //                   font_id: "font-default-18826904"
            //                 },
            //                 {
            //                   type: "text",
            //                   value: "",
            //                   font_id: "font-95009e76"
            //                 }
            //               ],
            //               name: "B0501",
            //               show_symbol: 1
            //             },
            //             {
            //               type: "text",
            //               value: " ℃ P：",
            //               font_id: "font-default-18826904"
            //             },
            //             {
            //               type: "field",
            //               id: "field35637505402092109201",
            //               field_type: "select",
            //               start_symbol: "(",
            //               end_symbol: ")",
            //               readonly: 0,
            //               deletable: 1,
            //               placeholder: "",
            //               tip: "",
            //               source_id: "",
            //               source_list: [
            //                 {
            //                   text: "20"
            //                 },
            //                 {
            //                   text: "21"
            //                 },
            //                 {
            //                   text: "22"
            //                 },
            //                 {
            //                   text: "23"
            //                 },
            //                 {
            //                   text: "24"
            //                 },
            //                 {
            //                   text: "25"
            //                 },
            //                 {
            //                   text: "26"
            //                 },
            //                 {
            //                   text: "27"
            //                 },
            //                 {
            //                   text: "28"
            //                 },
            //                 {
            //                   text: "29"
            //                 },
            //                 {
            //                   text: "30"
            //                 },
            //                 {
            //                   text: "31"
            //                 },
            //                 {
            //                   text: "32"
            //                 },
            //                 {
            //                   text: "33"
            //                 },
            //                 {
            //                   text: "34"
            //                 },
            //                 {
            //                   text: "35"
            //                 },
            //                 {
            //                   text: "36"
            //                 },
            //                 {
            //                   text: "37"
            //                 },
            //                 {
            //                   text: "38"
            //                 },
            //                 {
            //                   text: "39"
            //                 },
            //                 {
            //                   text: "40"
            //                 },
            //                 {
            //                   text: "41"
            //                 },
            //                 {
            //                   text: "42"
            //                 },
            //                 {
            //                   text: "43"
            //                 },
            //                 {
            //                   text: "44"
            //                 },
            //                 {
            //                   text: "45"
            //                 },
            //                 {
            //                   text: "46"
            //                 },
            //                 {
            //                   text: "47"
            //                 },
            //                 {
            //                   text: "48"
            //                 },
            //                 {
            //                   text: "49"
            //                 },
            //                 {
            //                   text: "50"
            //                 },
            //                 {
            //                   text: "51"
            //                 },
            //                 {
            //                   text: "52"
            //                 },
            //                 {
            //                   text: "53"
            //                 },
            //                 {
            //                   text: "54"
            //                 },
            //                 {
            //                   text: "55"
            //                 },
            //                 {
            //                   text: "56"
            //                 },
            //                 {
            //                   text: "57"
            //                 },
            //                 {
            //                   text: "58"
            //                 },
            //                 {
            //                   text: "59"
            //                 },
            //                 {
            //                   text: "60"
            //                 },
            //                 {
            //                   text: "61"
            //                 },
            //                 {
            //                   text: "62"
            //                 },
            //                 {
            //                   text: "63"
            //                 },
            //                 {
            //                   text: "64"
            //                 },
            //                 {
            //                   text: "65"
            //                 },
            //                 {
            //                   text: "66"
            //                 },
            //                 {
            //                   text: "67"
            //                 },
            //                 {
            //                   text: "68"
            //                 },
            //                 {
            //                   text: "69"
            //                 },
            //                 {
            //                   text: "70"
            //                 },
            //                 {
            //                   text: "71"
            //                 },
            //                 {
            //                   text: "72"
            //                 },
            //                 {
            //                   text: "73"
            //                 },
            //                 {
            //                   text: "74"
            //                 },
            //                 {
            //                   text: "75"
            //                 },
            //                 {
            //                   text: "76"
            //                 },
            //                 {
            //                   text: "77"
            //                 },
            //                 {
            //                   text: "78"
            //                 },
            //                 {
            //                   text: "79"
            //                 },
            //                 {
            //                   text: "80"
            //                 },
            //                 {
            //                   text: "81"
            //                 },
            //                 {
            //                   text: "82"
            //                 },
            //                 {
            //                   text: "83"
            //                 },
            //                 {
            //                   text: "84"
            //                 },
            //                 {
            //                   text: "85"
            //                 },
            //                 {
            //                   text: "86"
            //                 },
            //                 {
            //                   text: "87"
            //                 },
            //                 {
            //                   text: "88"
            //                 },
            //                 {
            //                   text: "89"
            //                 },
            //                 {
            //                   text: "90"
            //                 },
            //                 {
            //                   text: "91"
            //                 },
            //                 {
            //                   text: "92"
            //                 },
            //                 {
            //                   text: "93"
            //                 },
            //                 {
            //                   text: "94"
            //                 },
            //                 {
            //                   text: "95"
            //                 },
            //                 {
            //                   text: "96"
            //                 },
            //                 {
            //                   text: "97"
            //                 },
            //                 {
            //                   text: "98"
            //                 },
            //                 {
            //                   text: "99"
            //                 },
            //                 {
            //                   text: "100"
            //                 }
            //               ],
            //               show_format: "",
            //               replace_format: "",
            //               meta: {},
            //               active_type: 0,
            //               multi_select: 0,
            //               max_width: 0,
            //               min_width: 0,
            //               style: {
            //                 id: "font-74862c64",
            //                 height: 16,
            //                 family: "宋体",
            //                 bold: false,
            //                 italic: false,
            //                 underline: false,
            //                 dblUnderLine: false,
            //                 strikethrough: false,
            //                 script: 3,
            //                 color: "#000",
            //                 bgColor: null
            //               },
            //               valid: 0,
            //               valid_content: "",
            //               children: [
            //                 {
            //                   type: "text",
            //                   value: "",
            //                   font_id: "font-95009e76"
            //                 },
            //                 {
            //                   type: "text",
            //                   value: "脉搏",
            //                   font_id: "font-default-18826904"
            //                 },
            //                 {
            //                   type: "text",
            //                   value: "",
            //                   font_id: "font-95009e76"
            //                 }
            //               ],
            //               name: "B0502",
            //               show_symbol: 1
            //             },
            //             {
            //               type: "text",
            //               value: " 次／分 R：",
            //               font_id: "font-default-18826904"
            //             },
            //             {
            //               type: "field",
            //               id: "field35637505402092109202",
            //               field_type: "select",
            //               start_symbol: "(",
            //               end_symbol: ")",
            //               readonly: 0,
            //               deletable: 1,
            //               placeholder: "",
            //               tip: "",
            //               source_id: "",
            //               source_list: [
            //                 {
            //                   text: "10"
            //                 },
            //                 {
            //                   text: "11"
            //                 },
            //                 {
            //                   text: "12"
            //                 },
            //                 {
            //                   text: "13"
            //                 },
            //                 {
            //                   text: "14"
            //                 },
            //                 {
            //                   text: "15"
            //                 },
            //                 {
            //                   text: "16"
            //                 },
            //                 {
            //                   text: "17"
            //                 },
            //                 {
            //                   text: "18"
            //                 },
            //                 {
            //                   text: "19"
            //                 },
            //                 {
            //                   text: "20"
            //                 },
            //                 {
            //                   text: "21"
            //                 },
            //                 {
            //                   text: "22"
            //                 },
            //                 {
            //                   text: "23"
            //                 },
            //                 {
            //                   text: "24"
            //                 },
            //                 {
            //                   text: "25"
            //                 },
            //                 {
            //                   text: "26"
            //                 },
            //                 {
            //                   text: "27"
            //                 },
            //                 {
            //                   text: "28"
            //                 },
            //                 {
            //                   text: "29"
            //                 },
            //                 {
            //                   text: "30"
            //                 },
            //                 {
            //                   text: "31"
            //                 },
            //                 {
            //                   text: "32"
            //                 },
            //                 {
            //                   text: "33"
            //                 },
            //                 {
            //                   text: "34"
            //                 },
            //                 {
            //                   text: "35"
            //                 },
            //                 {
            //                   text: "36"
            //                 },
            //                 {
            //                   text: "37"
            //                 },
            //                 {
            //                   text: "38"
            //                 },
            //                 {
            //                   text: "39"
            //                 },
            //                 {
            //                   text: "40"
            //                 }
            //               ],
            //               show_format: "",
            //               replace_format: "",
            //               meta: {},
            //               active_type: 0,
            //               multi_select: 0,
            //               max_width: 0,
            //               min_width: 0,
            //               style: {
            //                 id: "font-43c19fc0",
            //                 height: 16,
            //                 family: "宋体",
            //                 bold: false,
            //                 italic: false,
            //                 underline: false,
            //                 dblUnderLine: false,
            //                 strikethrough: false,
            //                 script: 3,
            //                 color: "#000",
            //                 bgColor: null
            //               },
            //               valid: 0,
            //               valid_content: "",
            //               children: [
            //                 {
            //                   type: "text",
            //                   value: "",
            //                   font_id: "font-95009e76"
            //                 },
            //                 {
            //                   type: "text",
            //                   value: "呼吸",
            //                   font_id: "font-default-18826904"
            //                 },
            //                 {
            //                   type: "text",
            //                   value: "",
            //                   font_id: "font-95009e76"
            //                 }
            //               ],
            //               name: "B0503",
            //               show_symbol: 1
            //             },
            //             {
            //               type: "text",
            //               value: "次／分 BP：",
            //               font_id: "font-default-18826904"
            //             },
            //             {
            //               type: "field",
            //               id: "field35637505402092109203",
            //               field_type: "select",
            //               start_symbol: "(",
            //               end_symbol: ")",
            //               readonly: 0,
            //               deletable: 1,
            //               placeholder: "",
            //               tip: "",
            //               source_id: "",
            //               source_list: [
            //                 {
            //                   text: "85"
            //                 },
            //                 {
            //                   text: "90"
            //                 },
            //                 {
            //                   text: "95"
            //                 },
            //                 {
            //                   text: "100"
            //                 },
            //                 {
            //                   text: "105"
            //                 },
            //                 {
            //                   text: "110"
            //                 },
            //                 {
            //                   text: "115"
            //                 },
            //                 {
            //                   text: "120"
            //                 },
            //                 {
            //                   text: "125"
            //                 },
            //                 {
            //                   text: "130"
            //                 },
            //                 {
            //                   text: "135"
            //                 },
            //                 {
            //                   text: "140"
            //                 },
            //                 {
            //                   text: "145"
            //                 },
            //                 {
            //                   text: "150"
            //                 },
            //                 {
            //                   text: "155"
            //                 },
            //                 {
            //                   text: "160"
            //                 },
            //                 {
            //                   text: "165"
            //                 },
            //                 {
            //                   text: "170"
            //                 },
            //                 {
            //                   text: "175"
            //                 },
            //                 {
            //                   text: "180"
            //                 },
            //                 {
            //                   text: "175"
            //                 },
            //                 {
            //                   text: "180"
            //                 }
            //               ],
            //               show_format: "",
            //               replace_format: "",
            //               meta: {},
            //               active_type: 0,
            //               multi_select: 0,
            //               max_width: 0,
            //               min_width: 0,
            //               style: {
            //                 id: "font-2216ea75",
            //                 height: 16,
            //                 family: "宋体",
            //                 bold: false,
            //                 italic: false,
            //                 underline: false,
            //                 dblUnderLine: false,
            //                 strikethrough: false,
            //                 script: 3,
            //                 color: "#000",
            //                 bgColor: null
            //               },
            //               valid: 0,
            //               valid_content: "",
            //               children: [
            //                 {
            //                   type: "text",
            //                   value: "",
            //                   font_id: "font-95009e76"
            //                 },
            //                 {
            //                   type: "text",
            //                   value: "血压1",
            //                   font_id: "font-default-18826904"
            //                 },
            //                 {
            //                   type: "text",
            //                   value: "",
            //                   font_id: "font-95009e76"
            //                 }
            //               ],
            //               name: "B05041",
            //               show_symbol: 1
            //             },
            //             {
            //               type: "text",
            //               value: "／",
            //               font_id: "font-default-18826904"
            //             },
            //             {
            //               type: "field",
            //               id: "field35637505402092109204",
            //               field_type: "select",
            //               start_symbol: "(",
            //               end_symbol: ")",
            //               readonly: 0,
            //               deletable: 1,
            //               placeholder: "",
            //               tip: "",
            //               source_id: "",
            //               source_list: [
            //                 {
            //                   text: "45"
            //                 },
            //                 {
            //                   text: "50"
            //                 },
            //                 {
            //                   text: "55"
            //                 },
            //                 {
            //                   text: "60"
            //                 },
            //                 {
            //                   text: "65"
            //                 },
            //                 {
            //                   text: "70"
            //                 },
            //                 {
            //                   text: "75"
            //                 },
            //                 {
            //                   text: "80"
            //                 },
            //                 {
            //                   text: "85"
            //                 },
            //                 {
            //                   text: "90"
            //                 },
            //                 {
            //                   text: "95"
            //                 },
            //                 {
            //                   text: "100"
            //                 },
            //                 {
            //                   text: "105"
            //                 },
            //                 {
            //                   text: "110"
            //                 },
            //                 {
            //                   text: "115"
            //                 },
            //                 {
            //                   text: "120"
            //                 },
            //                 {
            //                   text: "125"
            //                 },
            //                 {
            //                   text: "130"
            //                 },
            //                 {
            //                   text: "135"
            //                 },
            //                 {
            //                   text: "140"
            //                 }
            //               ],
            //               show_format: "",
            //               replace_format: "",
            //               meta: {},
            //               active_type: 0,
            //               multi_select: 0,
            //               max_width: 0,
            //               min_width: 0,
            //               style: {
            //                 id: "font-23e1a74f",
            //                 height: 16,
            //                 family: "宋体",
            //                 bold: false,
            //                 italic: false,
            //                 underline: false,
            //                 dblUnderLine: false,
            //                 strikethrough: false,
            //                 script: 3,
            //                 color: "#000",
            //                 bgColor: null
            //               },
            //               valid: 0,
            //               valid_content: "",
            //               children: [
            //                 {
            //                   type: "text",
            //                   value: "",
            //                   font_id: "font-95009e76"
            //                 },
            //                 {
            //                   type: "text",
            //                   value: "血压2",
            //                   font_id: "font-default-18826904"
            //                 },
            //                 {
            //                   type: "text",
            //                   value: "",
            //                   font_id: "font-95009e76"
            //                 }
            //               ],
            //               name: "B05042",
            //               show_symbol: 1
            //             },
            //             {
            //               type: "text",
            //               value: "mmHg",
            //               font_id: "font-default-18826904"
            //             }
            //           ],
            //           title_length: 0,
            //           content_padding_left: 0,
            //           vertical_align: "top",
            //           level: 0
            //         }
            //       ],
            //       groups: [],
            //       fontMap: {
            //         "font-24cb12b4": {
            //           id: "font-24cb12b4",
            //           height: 21.5,
            //           family: "宋体",
            //           bold: false,
            //           italic: false,
            //           underline: false,
            //           dblUnderLine: false,
            //           strikethrough: false,
            //           script: 3,
            //           color: "blue",
            //           bgColor: null
            //         },
            //         "font-88b16d6c": {
            //           id: "font-88b16d6c",
            //           height: 21.5,
            //           family: "宋体",
            //           bold: false,
            //           italic: false,
            //           underline: false,
            //           dblUnderLine: false,
            //           strikethrough: false,
            //           script: 3,
            //           color: "#000",
            //           bgColor: null
            //         },
            //         "font-3371eb6b": {
            //           id: "font-3371eb6b",
            //           height: 19,
            //           family: "宋体",
            //           bold: false,
            //           italic: false,
            //           underline: false,
            //           dblUnderLine: false,
            //           strikethrough: false,
            //           script: 3,
            //           color: "blue",
            //           bgColor: null
            //         },
            //         "font-53f08810": {
            //           id: "font-53f08810",
            //           height: 19,
            //           family: "宋体",
            //           bold: false,
            //           italic: false,
            //           underline: false,
            //           dblUnderLine: false,
            //           strikethrough: false,
            //           script: 3,
            //           color: "#000",
            //           bgColor: null
            //         },
            //         "font-95009e76": {
            //           id: "font-95009e76",
            //           height: 16,
            //           family: "宋体",
            //           bold: false,
            //           italic: false,
            //           underline: false,
            //           dblUnderLine: false,
            //           strikethrough: false,
            //           script: 3,
            //           color: "blue",
            //           bgColor: null
            //         },
            //         "font-default-18826904": {
            //           id: "font-default-18826904",
            //           height: 16,
            //           family: "宋体",
            //           bold: false,
            //           italic: false,
            //           underline: false,
            //           dblUnderLine: false,
            //           strikethrough: false,
            //           script: 3,
            //           color: "#000",
            //           bgColor: null
            //         },
            //         "font-238a7475": {
            //           id: "font-238a7475",
            //           height: 16,
            //           family: "宋体",
            //           bold: false,
            //           italic: false,
            //           underline: false,
            //           dblUnderLine: false,
            //           strikethrough: false,
            //           script: 3,
            //           color: "black",
            //           bgColor: null
            //         },
            //         "font-a63d0ef2": {
            //           id: "font-a63d0ef2",
            //           height: 15,
            //           family: "仿宋",
            //           bold: false,
            //           italic: false,
            //           underline: false,
            //           dblUnderLine: false,
            //           strikethrough: false,
            //           script: 3,
            //           color: "blue",
            //           bgColor: null
            //         },
            //         "font-342713ff": {
            //           id: "font-342713ff",
            //           height: 15,
            //           family: "仿宋",
            //           bold: false,
            //           italic: false,
            //           underline: false,
            //           dblUnderLine: false,
            //           strikethrough: false,
            //           script: 3,
            //           color: "#000",
            //           bgColor: null
            //         }
            //       },
            //       bodyText: "",
            //       config: {
            //         direction: "vertical",
            //         header_horizontal: true,
            //         footer_horizontal: false
            //       },
            //       meta: {}
            //     },
            //     align: "left"
            //   },
            //   {
            //     type: "content",
            //     value: [{
            //       id: "para-ff72388d",
            //       type: "p",
            //       align: "left",
            //       deepNum: 0,
            //       islist: false,
            //       isOrder: false,
            //       indentation: 0,
            //       dispersed_align: false,
            //       before_paragraph_spacing: 0,
            //       restart_list_index: false,
            //       row_ratio: 1.6,
            //       page_break: false,
            //       children: [
            //         {
            //           type: "text",
            //           value: "\t一般情况阿斯利康大数据的ask了大家阿斯利康大家奥卡拉圣诞节卡了数据的卡拉圣诞节啊难得放假看了哈表达式士大夫艰苦丽莎的恐惧愤怒是大家看法呢啊解开了士大夫你艰苦拉萨能否尽快打算能否尽快爱上你的饭卡单反就卡死的女方家按三方接口单反金卡单反教案安居客反击看到NSF骄傲单发但凡金卡当你发觉扣税的妇女就拿上帝就发你氨基酸的看法呢静安寺能否尽快按三方接口蓝色艰苦拉萨你的就卡死的女方家看啊打算能否尽快安定三方接口懒得三方接口那附近恐龙蛋就看上的飞机啊丹凤街按时地方就按多少积分那是对方年卡南方",
            //           font_id: "font-43a1bea6"
            //         },
            //         {
            //           type: "field",
            //           id: "6760398601539683460",
            //           field_type: "select",
            //           start_symbol: "(",
            //           end_symbol: ")",
            //           display_type: "normal",
            //           readonly: 0,
            //           deletable: 1,
            //           placeholder: "健康情况",
            //           tip: "",
            //           source_id: "6760398601539683460",
            //           source_list: [],
            //           show_format: 0,
            //           replace_format: 0,
            //           number_format: 0,
            //           meta: {},
            //           active_type: 0,
            //           multi_select: 0,
            //           separator: 0,
            //           inputMode: 0,
            //           align: "left",
            //           max_width: 0,
            //           min_width: 0,
            //           style: {},
            //           cascade_list: [],
            //           automation_list: [],
            //           valid: 0,
            //           valid_content: {
            //             require: true,
            //             type: "string",
            //             phone_type: "",
            //             rule: {
            //               max_length: 20,
            //               min_length: 5
            //             },
            //             regex: ""
            //           },
            //           box_checked: 0,
            //           box_multi: 0,
            //           children: [],
            //           name: "eleNode",
            //           show_symbol: 1,
            //           show_field: true,
            //           formula: "",
            //           formula_value: 0,
            //           forbidden: {
            //             start: null,
            //             end: null
            //           },
            //           maxHeight: 0,
            //           font_id: "font-43a1bea6"
            //         },
            //         {
            //           type: "text",
            //           value: "，",
            //           font_id: "font-43a1bea6"
            //         },
            //         {
            //           type: "field",
            //           id: "4875258910714757120",
            //           field_type: "select",
            //           start_symbol: "(",
            //           end_symbol: ")",
            //           display_type: "normal",
            //           readonly: 0,
            //           deletable: 1,
            //           placeholder: "姑侄",
            //           tip: "",
            //           source_id: "4875258910714757120",
            //           source_list: [],
            //           show_format: 0,
            //           replace_format: 0,
            //           number_format: 0,
            //           meta: {},
            //           active_type: 0,
            //           multi_select: 0,
            //           separator: 0,
            //           inputMode: 0,
            //           align: "left",
            //           max_width: 0,
            //           min_width: 0,
            //           style: {},
            //           cascade_list: [],
            //           automation_list: [],
            //           valid: 0,
            //           valid_content: {
            //             require: true,
            //             type: "string",
            //             phone_type: "",
            //             rule: {
            //               max_length: 20,
            //               min_length: 5
            //             },
            //             regex: ""
            //           },
            //           box_checked: 0,
            //           box_multi: 0,
            //           children: [],
            //           name: "eleNode",
            //           show_symbol: 1,
            //           show_field: true,
            //           formula: "",
            //           formula_value: 0,
            //           forbidden: {
            //             start: null,
            //             end: null
            //           },
            //           maxHeight: 0,
            //           font_id: "font-43a1bea6"
            //         },
            //         {
            //           type: "field",
            //           id: "4858094238337074308",
            //           field_type: "select",
            //           start_symbol: "(",
            //           end_symbol: ")",
            //           display_type: "normal",
            //           readonly: 0,
            //           deletable: 1,
            //           placeholder: "与患者关系",
            //           tip: "",
            //           source_id: "4858094238337074308",
            //           source_list: [],
            //           show_format: 0,
            //           replace_format: 0,
            //           number_format: 0,
            //           meta: {},
            //           active_type: 0,
            //           multi_select: 0,
            //           separator: 0,
            //           inputMode: 0,
            //           align: "left",
            //           max_width: 0,
            //           min_width: 0,
            //           style: {},
            //           cascade_list: [],
            //           automation_list: [],
            //           valid: 0,
            //           valid_content: {
            //             require: true,
            //             type: "string",
            //             phone_type: "",
            //             rule: {
            //               max_length: 20,
            //               min_length: 5
            //             },
            //             regex: ""
            //           },
            //           box_checked: 0,
            //           box_multi: 0,
            //           children: [],
            //           name: "eleNode",
            //           show_symbol: 1,
            //           show_field: true,
            //           formula: "",
            //           formula_value: 0,
            //           forbidden: {
            //             start: null,
            //             end: null
            //           },
            //           maxHeight: 0,
            //           font_id: "font-43a1bea6"
            //         },
            //         {
            //           type: "text",
            //           value: "啊实打实阿松大阿松大阿松大阿松大阿松大阿松大阿松大。",
            //           font_id: "font-43a1bea6"
            //         }
            //       ],
            //       title_length: 0,
            //       content_padding_left: 0,
            //       vertical_align: "top",
            //       level: 0,
            //       listNumStyle: "number",
            //       itemsWidth: []
            //     }]
            //   }
            // ];

            // // 删除关键字中间的所有段落 ↓
            // const keywords = ["体格检查", "专科检查", "辅助检查"];
            // const rawData = editor.ext.deleteParasBetweenKeywords(keywords);
            // console.log("删除成功了吗", rawData);
            // editor.ext.insertData(params);
            // // 删除关键字中间的所有段落 ↑

            // 获取文本域内容描述和恢复 ↓
            // const field = editor.getFieldsByName("test")[0];
            // const description = field.getContentDescription();
            // console.log(description, "描述");
            // setTimeout(() => {
            //   const field = editor.getFieldsByName("recovery")[0];
            //   field.recoveryByDescription(description);
            //   editor.refreshDocument();
            // }, 1000);
            // 获取文本域内容描述和恢复 ↑

            // editor.deleteEmtptyParagraphOnTblSide();
            // const data = JSON.parse(localStorage.getItem("data"));
            // console.log("模板", data);
            // editor.changePageSize("A4", "vertical");
            // console.log(...editor.selection.focus);
            // // for (let i = 0; i < 2; i++) {
            // editor.insertTemplateData(data);
            // }
            // const fields = editor.getAllFields();
            // const field = editor.insertField({
            //   id: "测试用的id",
            //   name: "测试用的name",
            //   placeholder: " ",
            //   start_symbol: "",
            //   end_symbol: ""
            // });
            // editor.updateFieldText({
            //   id: "测试用的id",
            //   value: "医生会gd广东撒广东撒发多少范德萨发多少范德萨范德萨发多少电风发多少诊" + "："
            // });
            // editor.locatePathOutField(field, "end");
            // editor.insertField();
            // editor.refreshDocument(true);
            // editor.removeFields(fields);
            // editor.refreshDocument();
            // const field = editor.getFieldsByName("conclusion");
            // console.log(field[0]?.text, "获取到的文本内容");
            // const res = editor.searchAll(["患者签名", "医生签名", "签名A", "家属签名", "亲属签名", "111", "患者方签名"], false);
            // console.log(res);
            // const para = editor.selection.getFocusParagraph();
            // const id = para.id;

            // // editor.enterEditHeaderAndFooterMode("footer");
            // const params = {
            //   paraId: id,
            //   targetText: "你好",
            //   targetIndex: 3,
            //   replaceText: "111111"

            // };
            // editor.setHighlightOrReplaceTextByParams(params);

            // editor.insertGroupWithDateSort(new Date().valueOf);

            // editor.reformatParagraph(editor.getAllParagraph(), false, false);
            // localStorage.setItem("test", (localStorage.getItem("test") * 1 + 1) + "");
            // const field1 = editor.getFieldsByName("t" + localStorage.getItem("test"))[0];
            // // const field2 = editor.getFieldsByName("t2")[0];
            // // const field3 = editor.getFieldsByName("t3")[0];
            // // const field4 = editor.getFieldsByName("t4")[0];
            // editor.setAdminMode(true);
            // editor.removeFields([field1]);
            // const para = editor.selection.getFocusParagraph();
            // console.log(para);

            // for (let i = 0; i < 4; i++) {
            //   editor.removeFields([]);
            // }

            // // 测试文本域追加内容 样式问题 ↓
            // const field = editor.getFieldById("field-1889654c");
            // field.setNewText("追加的内容");
            // editor.updateFieldText({
            //   fields: [field],
            //   append: true
            // });
            // console.log(field);
            // // 测试文本域追加内容 样式问题 ↑

            // // 测试中草药 ↓
            // const data1 = ["蜜麻黄15g", "桂枝15g", "漏芦20g", "炒苦杏仁15g",
            //   "北柴胡15g", "粉葛20g", "羌活15g", "白芷15g",
            //   "生石膏30g[先煎]", "炒牛芳子15g", "蝉蜕15g", "玄参15g",
            //   "姜黄10g", "炒僵蚕发多少啊10g", "麦冬35g", "知母20g",
            //   "黄柏20g", "炒酸枣仁20g", "柏子仁20g"];

            // // for (let i = 0; i < 3; i++) {
            // // 五加皮(定）1袋 五味子（定）2袋 地榆1g 射干1g  乌梅1g 五灵脂1g
            // const data2 = ["五加皮(定）1袋", "五味子（定）2袋", "地榆1g", "射干1g", "乌梅1g", "五灵脂1g"];
            // const arr = [data1, data2];
            // console.log("新");
            // for (let n = 0; n < 1; n++) {
            //   console.count("次数");
            //   const startPara = editor.selection.getFocusParagraph();
            //   let insertText = "";
            //   for (let i = 0; i < arr[n].length; i++) {
            //     insertText += arr[n][i] + ((i + 1) % 4 === 0 ? "\n" : " ");
            //   }
            //   editor.insertText(insertText);

            //   const endPara = editor.selection.getFocusParagraph();
            //   editor.formatParagraph(editor.root_cell.paragraph.slice(startPara.para_index, endPara.para_index + 1));
            //   editor.insertText("\n" + "七剂 一日一剂 水煎服");

            //   editor.changeContentAlign("right");
            //   editor.insertText("\n");
            //   editor.changeContentAlign("left");
            // }
            // // 测试中草药 ↑

            // }
            // console.log(insertText);
            // const paragraphs = editor.root_cell.paragraph.slice(1, 6);
            // editor.formatParagraph(paragraphs, 7);
            // editor.setAdminMode();
            // editor.updateFieldText({
            //   name: "B0002",
            //   value: ""
            // });
            // const allParas = editor.getAllParagraph();
            // editor.firstRowIndentation(0, allParas);
            // editor.reformatParagraph([], false, true);

            // const uuid = "xxxxxxxx".replace(/[xy]/g, (c) => {
            //   const r = (Math.random() * 16) | 0; // 效果等于 parseInt(Math.random() * 16) 就是取整
            //   const v = c === "x" ? r : (r & 0x3) | 0x8;
            //   return v.toString(16);
            // });

            // editor.insertGroupWithDateSort("2022-11-11 12:14:12", "subDoc-" + uuid);
            // const group = editor.selection.getFocusGroup();
            // const field_time_title = { // 分组标题-时间标题
            //   id: group.id + "-Time",
            //   name: group.id + "-Time",
            //   type: "label",
            //   style: {
            //     height: 20,
            //     bold: true
            //   },
            //   placeholder: "",
            //   start_symbol: "",
            //   end_symbol: "",
            //   show_format: 0,
            //   replace_format: 0,
            //   readonly: 0,
            //   deletable: 0,
            //   source_id: "",
            //   label_text: "2022-11-11 12:14:12",
            //   multi_select: 0,
            //   active_type: 0
            // };
            // const field_record_title = { // 分组标题-分组标题
            //   id: group.id + "-Record",
            //   name: group.id + "-Record",
            //   type: "label",
            //   placeholder: "",
            //   start_symbol: "",
            //   style: {
            //     height: 20,
            //     bold: true
            //   },
            //   end_symbol: "",
            //   show_format: 0,
            //   replace_format: 0,
            //   readonly: 0,
            //   deletable: 0,
            //   source_id: "",
            //   label_text: "收拾收拾",
            //   multi_select: 0,
            //   active_type: 0
            // };
            // editor.insertAnchorField();
            // editor.insertField(field_record_title);
            // setTimeout(() => {
            //   console.log(editor.config.default_font_style.height, "height1");
            //   const rawData = editor.getRawData();
            //   console.log(editor.config.default_font_style.height, "height2");
            //   editor.reInitRaw(rawData);
            //   console.log(editor.config.default_font_style.height, "height3");
            //   editor.refreshDocument();
            //   console.log(1111111111);
            // }, 3000);

            // editor.updateFieldText({
            //   name: "B0409",
            //   replace: true,
            //   value: "热证"
            // });
            // const field = editor.getFieldsByName("B0409")[0];
            // editor.selection.setCursorPosition(editor.paraPath2ModelPath(field.end_para_path_outer));
            // editor.refreshDocument();
            // editor.insertAnchorField();
            // editor.createFloatModel(800, 600, [400, 200]);
            // editor.toggleRowLineType(editor.config.rowLineType === 0 ? 1 : 0);
            // const data = editor.getGroupRawData();
            // console.log(data);
            // console.time("setViewMode");
            // if (editor.view_mode === "normal") {
            //   editor.setViewMode("view");
            // } else {
            //   editor.setViewMode("normal");
            // }
            // console.timeEnd("setViewMode");
            // const fields = editor.current_cell.fields;
            // console.log(fields, "all fields");
            // for (const field of fields) {
            //   editor.setCharacterSize(
            //     field,
            //     "bigger",
            //     undefined,
            //     160,
            //     true
            //   );
            // }
            // editor.clearFields("ceshi ");
            // const field = editor.getFieldById("zoom");
            // editor.setCharacterSize(field, "bigger", undefined, 100, true);
            // const field1 = editor.getFieldById("field-c4e2025a");
            // const field2 = editor.getFieldById("field-2317e645");
            // const cells = editor.root_cell.children[4].children.filter((_, index) => index === 8 || index === 9);
            // editor.reviseFieldAttr({ field: field1, max_width: cells[0].width - cells[0].padding_left - cells[0].padding_right - 16 });
            // editor.reviseFieldAttr({ field: field2, max_width: cells[1].width - cells[1].padding_left - cells[1].padding_right - 16 });
            // editor.refreshDocument();
            // const group = editor.root_cell.groups[0];
            // const data = editor.getGroupRawData(group);
            // console.log(data, "打印数据");
            // const style = { family: "宋体", bold: false, italic: false, height: 56.5, underline: false, dblUnderLine: false, strikethrough: false, script: 3, color: "#000", bgColor: "rgba(255,255,255,0)" };
            // editor.change_font_style(style);
            // const couter = 0;
            // const timerId = null;
            // editor.insertRowAndColByName("selfFundOrder", val.length - 1, "");
            // const table = editor.getTablesByName("selfFundOrder");
            // console.log("tabletable", table);
            // const cell0 = table[0].getCellByPosition(3, 3);
            // cell0.appendText("1243");
            // editor.render();
            // for (let i = 0; i < 300; i++) {
            //   editor.setViewMode("normal");
            //   editor.setViewMode("form");
            // }
            // console.log("End");
            // timerId = setInterval(() => {
            //   couter += 1;
            //   if (couter >= 300) {
            //     timerId && clearInterval(timerId);
            //   }
            //   console.log(couter);
            //   editor.refreshDocument(true);
            // }, );
            // const data = editor.checkSensitiveWord(["道"], { bgColor: "rgb(255, 224, 224)" });
            // console.log(data, "测试敏感词");
            // editor.deleteInImageTable("image-type--196e027a");
            // console.log(split_row_num.value, split_col_num.value);
            // const colNum = Number(split_col_num.value) - 1 >= 0 ? Number(split_col_num.value) - 1 : 0;
            // const rowNum = Number(split_row_num.value) - 1 >= 0 ? Number(split_row_num.value) - 1 : 0;
            // editor.splitCellNotHaveBeenMerged(rowNum, colNum);
            // const field = editor.selection.getFocusField();
            // editor.showOrHideField(field);
            // editor.highlightByKeywordSubscripts({
            //   keywords:
            //   [
            //     {
            //       upset: 690,
            //       offset: 692,
            //       name: "啰音"
            //     },
            //     {
            //       upset: 116,
            //       offset: 118,
            //       name: "罗音"
            //     },
            //     {
            //       upset: 675,
            //       offset: 677,
            //       name: "啰音"
            //     },
            //     {
            //       upset: 631,
            //       offset: 633,
            //       name: "啰音"
            //     },
            //     {
            //       upset: 49,
            //       offset: 51,
            //       name: "咳嗽"
            //     }
            //   ],
            //   groupId: "subDoc-541c96dd"
            // });
            // const field = editor.selection.getFocusField();
            // console.log(field, "打印文本域");
            // editor.quitEditHeaderAndFooterMode();

            // const fields = editor.getAllFields(editor.footer_cell);
            // for (let i = 0; i < fields.length; i++) {
            //   const field = fields[i];
            //   editor.reviseFieldAttr({
            //     start_symbol: "[",
            //     end_symbol: "]",
            //     field: field
            //   });
            //   console.log(field.children);
            //   // const tempplateInfo =
            //   //   patInfoTemplate[field.id] || patInfoTemplate[field.name];
            //   field.setNewText("ceshi");
            //   editor.updateFieldText({ fields: [field], append: true });
            // }

            // editor.setReadonly(true);
            // editor.setViewMode("form");
            // console.log(editor.getFieldsByPlaceholder(["表格", "表格2", "表格3", "页眉"]));
            // Editor.config.setFontSize("小三");
            // const characters = editor.change_font_style({ height: Editor.config.getFontSize() });
            // for (let i = 0; i < characters.length; i++) {
            //   characters[i].mark = "测试用的";
            // }
            // // const table = editor.current_cell.paragraph[1];
            // // table.remove();
            // // editor.refreshDocument();
            // const res = editor.getPositionRelativeToPageLeftBottom("医生签名:");
            // console.log(res, "打印结果");
            // editor.adminMode = !editor.adminMode;

            // // 插入图片排版 - 旧版 ↓
            // const field = editor.getFieldById("checkBody");
            // editor.insertTableAfterField(field);
            // const insertField = editor.createElement("field");
            // insertField.type = "select";
            // insertField.source_list = [
            //   {
            //     code: "检索1",
            //     text: "选项1"
            //   },
            //   {
            //     code: "检索2",
            //     text: "选项2"
            //   },
            //   {
            //     code: "检索3",
            //     text: "选项3"
            //   }
            // ];
            // editor.insertImageTable({
            //   src: "https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fimg2.baidu.com%2Fit%2Fu%3D98371021%2C1121096365%26fm%3D253%26app%3D120%26f%3DJPEG%3Fw%3D1200%26h%3D800&refer=http%3A%2F%2Fimg2.baidu.com&app=2002&size=w300&q=a80&n=0&g=0n&fmt=jpeg?sec=1645582607&t=cc63571e03f0e52e3493f1edb6e47b57",
            //   id: Math.random(), // 可不传
            //   field: insertField,
            //   needSerialNum: true // 可不传
            // });
            // // 插入图片排版 - 旧版 ↑

            // // 插入排版 - 新版 ↓
            // const ratio = [3, 3, 4];

            // const srcs = ["https://img1.baidu.com/it/u=1394459067,3018342203&fm=253&fmt=auto&app=120&f=JPEG?w=353&h=500", "https://view-cache.book118.com/view3/M01/29/07/wKh2BF2nVC6AFjuyAAAYi5A8DEM243.png", "https://img0.baidu.com/it/u=1944966311,750301424&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500", "https://img0.baidu.com/it/u=405086877,2666710572&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500", "https://view-cache.book118.com/view3/M04/0A/20/wKh2BV2nVC6AW4GkAAAYeJykwPU260.png"];
            // const total = ratio.reduce((t, c) => t + c, 0);
            // const data = [];
            // for (let i = 0; i < total; i++) {
            //   const selectField = editor.createElement("field");
            //   selectField.type = "select";
            //   selectField.source_list = [
            //     {
            //       code: "检索1",
            //       text: "选项1"
            //     },
            //     {
            //       code: "检索2",
            //       text: "选项2"
            //     },
            //     {
            //       code: "检索3",
            //       text: "选项3"
            //     },
            //     {
            //       code: "检索4",
            //       text: "选项4"
            //     },
            //     {
            //       code: "检索5",
            //       text: "选项5"
            //     }
            //   ];
            //   data.push({ src: srcs[Math.floor(Math.random() * srcs.length)], serialNum: i + "", selectField, meta: { id: Math.random(), t: "ce" }, defaultCode: "检索1" });
            // }

            // const table = editor.insertLayout(ratio, data, 92, 74, 24);
            // table.meta = { a: "ceshi", B: "测试用的b", c: ["c里边的数组元素1"] };
            // // 插入排版 ↑
          }
        },
        {
          title: "打开文件",
          exe () {
            const openFileChange = (e) => {
              const target = e.target;
              const file = target.files[0];
              if (file) {
                this.loading = true;
                file.text().then(function (res) {
                  console.time("打开文件");
                  editor.reInitRawByConfig(res);
                  editor.refreshDocument();
                  console.timeEnd("打开文件");
                })
                  .finally(function () {
                    target.remove();
                  });
              }
            };
            const input = document.createElement("input");
            input.hidden = "";
            input.type = "file";
            input.accept = ".xml,.txt,.json,.grf,.html";
            input.onchange = openFileChange;
            input.click();
          }
        },
        {
          title: "对比rawData",
          exe () {
            const data1 = ["蜜麻黄15g", "桂枝15g", "漏芦20g", "炒苦杏仁15g",
              "北柴胡15g", "粉葛20g", "羌活15g", "白芷15g",
              "生石膏30g[先煎]", "炒牛芳子15g", "蝉蜕15g", "玄参15g",
              "姜黄10g", "炒僵蚕发多少啊10g", "麦冬35g", "知母20g",
              "黄柏20g", "炒酸枣仁20g", "柏子仁20g"];

            // for (let i = 0; i < 3; i++) {
            // 五加皮(定）1袋 五味子（定）2袋 地榆1g 射干1g  乌梅1g 五灵脂1g
            const data2 = ["五加皮(定）1袋", "五味子（定）2袋", "地榆1g", "射干1g", "乌梅1g", "五灵脂1g"];
            const arr = [data1, data2];
            for (let n = 1; n < 2; n++) {
              const startPara = editor.selection.getFocusParagraph();
              let insertText = "";
              for (let i = 0; i < arr[n].length; i++) {
                insertText += arr[n][i] + ((i + 1) % 4 === 0 ? "\n" : " ");
              }
              editor.insertText(insertText);

              const endPara = editor.selection.getFocusParagraph();
              editor.formatParagraph(editor.root_cell.paragraph.slice(startPara.para_index, endPara.para_index + 1));
              editor.insertText("\n" + "七剂 一日一剂 水煎服");

              editor.changeContentAlign("right");
              editor.insertText("\n");
              editor.changeContentAlign("left");
            }
            // const raw1 = localStorage.getItem("rawData1");
            // const raw2 = localStorage.getItem("rawData2");
            // if (!raw1 || !raw2) {
            //   return alert("localStorge中缺少rawData1或rawData2");
            // }
            // const res = editor.compareRawData(raw1, raw2);
            // if (res) {
            //   alert("相同");
            // } else {
            //   alert("不相同");
            // }
          }
        },
        {
          title: "插入条形码",
          exe () {
            editor.insertFloatBarcode("123asdaas123", { type: "barcode", options: { format: "UPCE" } });
          }
        },
        {
          title: "插入水印",
          exe () {
            editor.insertItalicMark("123asdaas123",
              {
                direction: "right",
                module: ["pageWrite", "printView", "printPaper"],
                font: {
                  fontSize: 16,
                  fontFamily: "仿宋",
                  opacity: 0.5,
                  color: "#7F7F7F"
                }
              });
          }
        },
        {
          title: "改变box宽",
          exe () {
            localStorage.setItem("cascadeBox", 1);
          }
        },
        {
          title: "行内居上",
          exe () {
            editor.change_font_style({ align: "top" });
          }
        },
        {
          title: "行内居中",
          exe () {
            editor.change_font_style({ align: "middle" });
          }
        },
        {
          title: "行内居下",
          exe () {
            editor.change_font_style({ align: "bottom" });
          }
        },
        {
          // 快速录入模式切换
          title: "快速录入模式",
          exe () {
            editor.config.fastEntryMode = !editor.config.fastEntryMode;
          }
        },
        { // 插入按钮
          title: "插入按钮",
          exe () {
            editor.insertButton("席忠的地方第三方第三方十多个电饭锅强", 100, 20);
          }
        },
        {
          title: "插入多选框",
          exe (e) {
            e.preventDefault();
            const items = [
              {
                name: "a1",
                checked: true,
                value: "啦啦啦",
                field_names: "aa,bb"
                // disabled: 1
              },
              {
                name: "a2",
                checked: false,
                value: "啦啦啦2",
                field_names: "cc,dd"
              }
            ];
            editor.insertWidget({ isGroup: true, border: "dotted", groupName: "group_box_1", isMulti: true, widgetType: "checkbox", deletable: 1, items });
          }
        }, {
          title: "插入单选框",
          exe (e) {
            e.preventDefault();
            const items = [
              {
                name: "a1",
                checked: true,
                value: "qweqweqwe"
              },
              {
                name: "a2",
                checked: false,
                value: "wwerwerewr"
              }
            ];
            editor.insertWidget({ isGroup: true, groupName: "group_box", widgetType: "radio", deletable: 1, items });
          }
        },
        {
          title: "插入卡尺",
          exe (e) {
            e.preventDefault();
            editor.insertCaliper(100, { num: 20, spacing: 20 });
          }
        },
        {
          title: "放大",
          exe (e) {
            e.preventDefault();
            let viewScale = editor.viewScale;
            viewScale += 0.1;
            editor.setViewScale(viewScale);
          }
        },
        {
          title: "放至最大",
          exe (e) {
            e.preventDefault();
            editor.setMaxViewScale();
          }
        },
        {
          title: "恢复默认值",
          exe (e) {
            e.preventDefault();
            editor.setViewScale();
          }
        },
        {
          title: "清空正文内容",
          exe (e) {
            e.preventDefault();
            editor.clearDocument();
          }
        },
        {
          title: "重新初始化配置",
          exe (e) {
            e.preventDefault();
            const config = {
              page_padding_left: 40,
              page_padding_right: 40
            };
            editor.reInitConfig(config);
          }
        },
        {
          title: "自适应大小",
          exe (e) {
            e.preventDefault();
            editor.adaptSize();
          }
        },
        {
          title: "获取选区数据",
          exe (e) {
            e.preventDefault();
            const rawData = editor.getRawDataBySelection();
            editor.selection.setCursorByRootCell("start");
            if (rawData) {
              editor.reInitRaw(rawData, false);
              editor.update();
              editor.scroll_by_focus();
              editor.render();
            }
          }
        }
      ]
    },
    {
      title: "文字样式修改",
      children: [
        {
          title: "初号",
          exe (e) {
            e.preventDefault();
            // Editor.config.setFontSize("初号");
            editor.change_font_style({ height: 97.5 });
          }
        },
        { // 字号增加
          title: "字号增",
          exe () {
            editor.setSelectionCharacterSize("bigger");
            editor.refreshDocument();
          }

        },
        { // 字号减少
          title: "字号减",
          exe () {
            editor.setSelectionCharacterSize("smaller");
            editor.refreshDocument();
          }
        },
        {
          title: "小初",
          exe (e) {
            e.preventDefault();
            Editor.config.setFontSize("小初");
            editor.change_font_style({ height: Editor.config.getFontSize() });
          }
        }, {
          title: "一号",
          exe (e) {
            e.preventDefault();
            Editor.config.setFontSize("一号");
            editor.change_font_style({ height: Editor.config.getFontSize() });
          }
        }, {
          title: "小一",
          exe (e) {
            e.preventDefault();
            Editor.config.setFontSize("小一");
            editor.change_font_style({ height: Editor.config.getFontSize() });
          }
        }, {
          title: "二号",
          exe (e) {
            e.preventDefault();
            Editor.config.setFontSize("二号");
            editor.change_font_style({ height: Editor.config.getFontSize() });
          }
        }, {
          title: "小二",
          exe (e) {
            e.preventDefault();
            Editor.config.setFontSize("小二");
            editor.change_font_style({ height: Editor.config.getFontSize() });
          }
        }, {
          title: "三号",
          exe (e) {
            e.preventDefault();
            Editor.config.setFontSize("三号");
            editor.change_font_style({ height: Editor.config.getFontSize() });
          }
        }, {
          title: "小三",
          exe (e) {
            e.preventDefault();
            Editor.config.setFontSize("小三");
            editor.change_font_style({ height: Editor.config.getFontSize() });
          }
        }, {
          title: "四号",
          exe (e) {
            e.preventDefault();
            Editor.config.setFontSize("四号");
            editor.change_font_style({ height: Editor.config.getFontSize() });
          }
        }, {
          title: "小四",
          exe (e) {
            e.preventDefault();
            Editor.config.setFontSize("小四");
            editor.change_font_style({ height: Editor.config.getFontSize() });
          }
        }, {
          title: "五号",
          exe (e) {
            e.preventDefault();
            Editor.config.setFontSize("五号");
            editor.change_font_style({ height: Editor.config.getFontSize() });
          }
        }, {
          title: "小五",
          exe (e) {
            e.preventDefault();
            Editor.config.setFontSize("小五");
            editor.change_font_style({ height: Editor.config.getFontSize() });
          }
        }, {
          title: "六号",
          exe (e) {
            e.preventDefault();
            Editor.config.setFontSize("六号");
            editor.change_font_style({ height: Editor.config.getFontSize() });
          }
        }, {
          title: "七号",
          exe (e) {
            e.preventDefault();
            Editor.config.setFontSize("七号");
            editor.change_font_style({ height: Editor.config.getFontSize() });
          }
        }, {
          title: "八号",
          exe (e) {
            e.preventDefault();
            Editor.config.setFontSize("八号");
            editor.change_font_style({ height: Editor.config.getFontSize() });
          }
        }, {
          title: "微软雅黑",
          exe (e) {
            e.preventDefault();
            editor.change_font_style({ family: "微软雅黑" });
          }
        }, {
          title: "仿宋",
          event: "`onmousedown`",
          exe (e) {
            e.preventDefault();
            editor.change_font_style({ family: "仿宋" });
          }
        }, {
          title: "宋体",
          exe (e) {
            e.preventDefault();
            editor.change_font_style({ family: "宋体" });
          }
        }, {
          title: "黑体",
          exe (e) {
            e.preventDefault();
            editor.change_font_style({ family: "黑体" });
          }
        }, {
          title: "楷体",
          exe (e) {
            e.preventDefault();
            editor.change_font_style({ family: "楷体" });
          }
        }, {
          title: "格式刷",
          exe (e) {
            e.preventDefault();
            editor.formatBrush();
          }
        }, {
          title: "清除样式",
          exe (e) {
            e.preventDefault();
            editor.deleteStyle();
          }
        },
        {
          title: "插入水平线",
          exe (e) {
            e.preventDefault();
            editor.insertLine(1, "red", null, "dash");
          }
        },
        {
          title: "加粗",
          exe (e) {
            e.preventDefault();
            editor.change_font_style({ bold: true });
          }
        },
        {
          title: "不加粗",
          exe (e) {
            e.preventDefault();
            editor.change_font_style({ bold: false });
          }
        },
        {
          title: "斜体",
          exe (e) {
            e.preventDefault();
            editor.change_font_style({ italic: true });
          }
        }, {
          title: "不斜体",
          exe (e) {
            e.preventDefault();
            editor.change_font_style({ italic: false });
          }
        }, {
          title: "上标",
          exe (e) {
            e.preventDefault();
            editor.change_font_style({ script: 1 });
          }
        }, {
          title: "下标",
          exe (e) {
            e.preventDefault();
            editor.change_font_style({ script: 2 });
          }
        }, {
          title: "取消上下标",
          exe (e) {
            e.preventDefault();
            editor.change_font_style({ script: 3 });
          }
        },
        {
          title: "下划线",
          exe (e) {
            e.preventDefault();
            editor.change_font_style({ underline: true });
          }
        },
        {
          title: "双下划线",
          exe (e) {
            e.preventDefault();
            editor.change_font_style({ dblUnderLine: true });
          }
        },
        {
          title: "取消双下划线",
          exe (e) {
            e.preventDefault();
            editor.change_font_style({ dblUnderLine: false, underline: false });
          }
        },
        {
          title: "取消下划线",
          exe (e) {
            e.preventDefault();
            editor.change_font_style({ underline: false });
          }
        }, {
          title: "删除线",
          exe (e) {
            e.preventDefault();
            editor.change_font_style({ strikethrough: true });
          }
        }, {
          title: "取消删除线",
          exe (e) {
            e.preventDefault();
            editor.change_font_style({ strikethrough: false });
          }
        }, {
          title: "红色",
          exe (e) {
            e.preventDefault();
            editor.change_font_style({ color: "red" });
          }
        }, {
          title: "黑色",
          exe (e) {
            e.preventDefault();
            editor.change_font_style({ color: "black" });
          }
        }, {
          title: "背景绿色",
          exe (e) {
            e.preventDefault();
            editor.change_font_style({ bgColor: "green" });
          }
        }, {
          title: "取消背景色",
          exe (e) {
            e.preventDefault();
            editor.change_font_style({ bgColor: null });
          }
        },
        {
          title: "插入分页符",
          exe (e) {
            e.preventDefault();
            editor.insertPageBreak();
          }
        }, {
          title: "右对齐",
          exe (e) {
            e.preventDefault();
            editor.changeContentAlign("right");
          }
        }, {
          title: "左对齐",
          exe (e) {
            e.preventDefault();
            editor.changeContentAlign("left");
          }
        }, {
          title: "居中对齐",
          exe (e) {
            e.preventDefault();
            editor.changeContentAlign("center");
          }
        }, {
          title: "上对齐",
          exe (e) {
            e.preventDefault();
            editor.setVerticalAlign("top");
          }
        }, {
          title: "上下居中",
          exe (e) {
            e.preventDefault();
            editor.setVerticalAlign("center");
          }
        }, {
          title: "下对齐",
          exe (e) {
            e.preventDefault();
            editor.setVerticalAlign("bottom");
          }
        },
        {
          title: "分散对齐",
          exe (e) {
            e.preventDefault();
            editor.setParaDispersed();
          }
        },
        {
          title: "文档对齐",
          exe (e) {
            e.preventDefault();
            editor.changeContentAlign("docuAlign");
          }
        },
        {
          title: "首行缩进",
          exe (e) {
            e.preventDefault();
            editor.firstRowIndentation(2);
          }
        }, {
          title: "取消首行缩进",
          exe (e) {
            e.preventDefault();
            editor.firstRowIndentation(0);
          }
        }, {
          title: "修改段间距",
          input: "prompt",
          exe (e, args) {
            e.preventDefault();
            editor.changeParaBeforSpacing(Number(args[0]));
          }
        },
        {
          title: "修改行倍距",
          input: "prompt",
          exe (e, args) {
            e.preventDefault();
            editor.changeRowRatio(Number(args[0]));
          }
        },
        {
          title: "段落重排",
          exe (e) {
            e.preventDefault();
            editor.reformatParagraph([], true, false);
          }
        }
      ]
    },
    {
      title: "保存",
      children: [
        {
          title: "保存",
          exe (e) {
            e.preventDefault();
            const rawData = editor.getRawData();
            localStorage.setItem("rawData", JSON.stringify(rawData));
          }
        },
        {
          title: "还原",
          exe (e) {
            e.preventDefault();
            localStorage.removeItem("rawData");
            editor.reInitRaw(Editor.rawData);
            editor.refreshDocument();
          }
        },
        {
          title: "导出json",
          exe (e) {
            e.preventDefault();
            editor.download("data.json");
          }
        },
        { // 测试数据压缩
          title: "数据压缩",
          exe () {
            if (editor.config.getDataType === 1) {
              editor.config.getDataType = 0;
              alert("已关闭");
            } else {
              editor.config.getDataType = 1;
              alert("已开启");
            }
          }
        },
        {
          title: "导出c++打印数据",
          exe (e) {
            e.preventDefault();
            editor.printCpp();
          }
        },
        {
          title: "log_nodes",
          exe (e) {
            e.preventDefault();
            // console.log("header", editor.header_cell);
            // console.log("content", editor.root_cell);
            // console.log("footer", editor.footer_cell);
            console.log("pages", editor.pages);
            console.log("selection", editor.selection);
            console.log(editor.root_cell);
          }
        }
      ]
    },
    {
      title: "模板",
      children: [
        {
          title: "都昌模板转换",
          exe (e) {
            e.preventDefault();
            const xml = localStorage.getItem("templateXML");
            if (!xml) {
              // eslint-disable-next-line no-undef
              return alert("请先在localStorage中设置templateXML");
            }
            editor.dcXmlDataTrans(xml);
            editor.reformatParagraph(
              editor.root_cell.paragraph,
              true,
              true
            );
          }
        },
        {
          title: "转成HTML",
          exe (e) {
            e.preventDefault();
            const html = editor.modelData2Html();
            editor.download("Test.html", html);
          }
        },
        {
          title: "插入模板",
          exe (e) {
            e.preventDefault();
            let templateRaw = localStorage.getItem("templateRaw");
            if (!templateRaw) {
              templateRaw = Editor.rawData;
            }
            // 到这儿说明已经插入完了 ↑
            editor.insertTemplateData(templateRaw, false, false, ["show_header_line", "show_footer_line"]);
          }
        },
        {
          title: "插入页眉页脚模板",
          exe (e) {
            e.preventDefault();
            let templateRaw = localStorage.getItem("templateRaw");
            if (!templateRaw) {
              templateRaw = Editor.rawData;
            }
            // 到这儿说明已经插入完了 ↑
            editor.insertHeaderOrFooterTemplateData(templateRaw);
          }
        }
      ]
    },
    {
      title: "视图",
      children: [
        {
          title: "表单模式切换",
          exe (e) {
            e.preventDefault();
            const view_mode = editor.view_mode === "normal" ? "form" : "normal";
            editor.setViewMode(view_mode);
          }
        },
        {
          title: "简洁视图切换",
          exe (e) {
            e.preventDefault();
            const view_mode = editor.view_mode === "normal" ? "view" : "normal";
            editor.setViewMode(view_mode);
          }
        }
      ]
    },
    {
      title: "列表",
      children: [
        {
          title: "段落编号",
          exe (e) {
            e.preventDefault();
            editor.restartListIndex();
          }
        },
        {
          title: "添加无序列表",
          exe (e) {
            e.preventDefault();
            editor.addList(false);
          }
        }, {
          title: "添加有序列表",
          exe (e) {
            e.preventDefault();
            editor.addList(true);
          }
        }, {
          title: "汉字序号",
          exe (e) {
            e.preventDefault();
            editor.changeListNumStyle("chinese");
          }
        }, {
          title: "数字序号",
          exe (e) {
            e.preventDefault();
            editor.changeListNumStyle("number");
          }
        }
      ]
    },
    {
      title: "页面设置",
      children: [
        {
          title: "A4",
          exe (e) {
            e.preventDefault();
            editor.changePageSize("A4", this.page_direction);
          }
        },
        {
          title: "A5",
          exe (e) {
            e.preventDefault();
            editor.changePageSize("A5", this.page_direction);
          }
        },
        {
          title: "A6",
          exe (e) {
            e.preventDefault();
            editor.changePageSize("A6", this.page_direction);
          }
        },
        {
          title: "B5",
          exe (e) {
            e.preventDefault();
            editor.changePageSize("B5", this.page_direction);
          }
        },
        {
          title: "B5(JIS)",
          exe (e) {
            e.preventDefault();
            editor.changePageSize("B5(JIS)", this.page_direction);
          }
        }, {
          title: "16K",
          exe (e) {
            e.preventDefault();
            editor.changePageSize("16K", this.page_direction);
          }
        }, {
          title: "16K(BIG)",
          exe (e) {
            e.preventDefault();
            editor.changePageSize("16K(BIG)", this.page_direction);
          }
        }, {
          title: "横向/纵向",
          exe (e) {
            e.preventDefault();
            this.is_vertical = !this.is_vertical;
            if (this.is_vertical) {
              this.page_direction = "vertical";
            } else {
              this.page_direction = "horizontal";
            }
            editor.changePageDirection(this.page_direction);
          }
        },
        {
          title: "自定义大小",
          exe (e) {
            e.preventDefault();
            editor.changeCustomPageSize(10, 30);
          }
        }
      ]
    },
    {
      title: "文本域",
      children: [
        {
          title: "开启关闭文本域公式",
          exe (e) {
            e.preventDefault();
            editor.formulaMode(!editor.formula_mode);
          }
        },
        {
          title: "文本域计算公式",
          exe (e) {
            e.preventDefault();
            const field = editor.selection.getFocusField();
            if (field) {
              field.formula = "[group_box_1]";
              // field.formula = "[name1]+[name2]*10";
            }
            editor.updateFieldsFormula(field);
          }
        },
        { // 测试修改页眉中的文本域
          title: "测试修改页眉中的文本域",
          exe () {
            const field = {
              id: "wardName-header",
              name: "B0819",
              tip: "",
              placeholder: "科别",
              type: "normal",
              start_symbol: "[",
              end_symbol: "]",
              readonly: 1,
              deletable: 1,
              max_width: 0,
              forbidden: {
                start: null,
                end: null
              },
              min_width: 0,
              show_format: 0,
              replace_format: 0,
              valid: 0,
              label_text: "普外科一病区",
              source_id: "",
              multi_select: 0,
              separator: 0,
              active_type: 0,
              show_field: true,
              source_list: [],
              cascade_list: [],
              valid_content: {
                require: 0,
                type: "",
                phone_type: "",
                rule: {
                  max_length: 20,
                  min_length: 0
                },
                regex: ""
              }
            };
            console.log("测试修改页眉中的文本域", field);
            editor.reviseFieldAttr(field);
            // editor.insertImageTable({ src: img_src, serialNum: i++, field: field, width: 300, height: 150 });
          }
        },
        { // 获取焦点文本域
          title: "获取焦点文本域",
          exe () {
            const field = editor.selection.getFocusField();
            console.log(field, "获取到焦点的文本域");
          }
        },
        { // 测试删除文本域
          title: "测试删除文本域",
          exe () {
            // const field = editor.getFieldById("test_field");
            console.log(editor.selection.getFieldByPath(editor.selection.para_focus), editor.selection.para_focus);
            // const fields = editor.getFieldsByName("B0002");
            // editor.removeFields(fields);
            // editor.insertImageTable({ src: img_src, serialNum: i++, field: field, width: 300, height: 150 });
          }
        },
        {
          title: "替换图片到文本域",
          exe (e) {
            e.preventDefault();
            const fields = editor.getFieldsByName("name");
            const image_info = {
              src: img_src,
              width: 150,
              height: 60
            };
            editor.replaceFieldsImage(fields, image_info);
          }
        }, {
          title: "替换图片到文本域2",
          exe (e) {
            e.preventDefault();
            const fields = editor.getFieldsByName("name");
            const image_info = {
              src: img_src,
              width: 150,
              height: 60,
              meta: {}// 扩展信息
            };
            const image = editor.createElement("image", image_info);
            editor.replaceFieldsElements(fields, [image]);
          }
        },
        {
          title: "插入输入域",
          exe (e) {
            e.preventDefault();
            // const field = {
            //   cascade_list: [{
            //     text: "aa",
            //     show_field_names: "aa,bb,cc",
            //   },
            //   {
            //     text: "啊啊",
            //     show_field_names: "安安",
            //   }]
            // };
            editor.insertField();
          }
        },
        {
          title: "测试表单模式下移除文本域报错",
          exe (e) {
            e.preventDefault();
            console.log("测试表单模式下移除所有文本域");
            // editor.setViewMode("form");
            const allFields = editor.getAllFields(editor.root_cell);
            editor.removeFields(allFields);
            // editor.setViewMode("normal");
          }
        },
        {
          title: "删除输入域",
          exe (e) {
            e.preventDefault();
            const field = editor.selection.getFocusField();
            if (field) {
              editor.removeFields([field]);
            }
          }
        },
        {
          title: "修改placeholder",
          input: "prompt",
          exe (e, args) {
            e.preventDefault();
            editor.reviseFieldAttr({ placeholder: args[0] });
          }
        },
        {
          title: "修改name",
          input: "prompt",
          exe (e, args) {
            e.preventDefault();
            editor.reviseFieldAttr({ name: args[0] });
          }
        },
        {
          title: "修改文本域展示样式",
          exe (e) {
            e.preventDefault();
            const field = editor.selection.getFocusField();
            editor.changeFieldDisplay("line", [field]);
          }
        },
        {
          title: "定位到name文本域",
          input: "prompt",
          exe (e, args) {
            e.preventDefault();
            editor.fixPositionToTargetField(null, args[0]);
          }
        },
        {
          title: "修改开始图标",
          input: "prompt",
          exe (e, args) {
            e.preventDefault();
            editor.reviseFieldAttr({ start_symbol: args[0] });
          }
        }, {
          title: "修改结束图标",
          input: "prompt",
          exe (e, args) {
            e.preventDefault();
            editor.reviseFieldAttr({ end_symbol: args[0] });
          }
        }, {
          title: "修改文本域ID",
          input: "prompt",
          exe (e, args) {
            e.preventDefault();
            const param = {
              id: args[0]
            };
            editor.reviseFieldAttr(param);
          }
        },
        {
          title: "替换元素到当前文本域",
          exe (e) {
            e.preventDefault();
            const field = editor.selection.getFocusField();
            if (field) {
              const first_row = editor.root_cell.children[0];
              editor.replaceFieldsElements([field], first_row.children);
            }
          }
        },
        {
          title: "文本域后插入图像集表格",
          exe (e) {
            e.preventDefault();
            editor.insertTableAfterField();
          }
        },
        {
          title: "替换name属性的文本域内容",
          input: "prompt",
          exe (e, args) {
            e.preventDefault();
            const checked = document.getElementById("field_is_append").checked;
            const field_content_value = args[0];
            const fields = [editor.focusElement.field];
            for (let i = 0; i < fields.length; i++) {
              const field = fields[i];
              if (field.type === "normal") {
                fields[i].setNewText(field_content_value);
              }
            }
            editor.updateFieldText({ fields: fields, append: checked });
            // editor.updateFieldText({
            //   fields: allFields,
            //   replace: true,
            //   value: "测试用\n的值"
            // });
            // input.current.focus();
          }
        },
        {
          title: "替换name属性的文本域内容为rawData",
          exe (e) {
            e.preventDefault();
            const rawData = `{"header":[{"id":"para-a5714a9c","type":"p","align":"center","deepNum":0,"islist":false,"isOrder":false,"indentation":0,"dispersed_align":false,"before_paragraph_spacing":0,"row_ratio":1.4,"page_break":false,"children":[{"type":"text","value":"","font_id":"font-aea83bfb"}],"title_length":0,"content_padding_left":0,"vertical_align":"top","level":0}],"footer":[{"id":"para-38e184eb","type":"p","align":"center","deepNum":0,"islist":false,"isOrder":false,"indentation":0,"dispersed_align":false,"before_paragraph_spacing":0,"row_ratio":1.4,"page_break":false,"children":[{"type":"text","value":"","font_id":"font-aea83bfb"}],"title_length":0,"content_padding_left":0,"vertical_align":"top","level":0}],"content":[{"id":"para-6faeb7a3","type":"p","align":"center","deepNum":0,"islist":false,"isOrder":false,"indentation":0,"dispersed_align":false,"before_paragraph_spacing":0,"row_ratio":1.4,"page_break":false,"children":[{"type":"text","value":"诊断:","font_id":"font-543a65e8"},{"type":"field","id":"field-52599670","field_type":"normal","start_symbol":"[","end_symbol":"]","readonly":0,"deletable":1,"placeholder":"入院诊断","tip":"","source_id":"","source_list":[],"show_format":0,"replace_format":0,"meta":{},"active_type":0,"multi_select":0,"max_width":0,"min_width":0,"style":{"id":"font-5f5afc70","height":19,"family":"宋体","bold":false,"italic":false,"underline":false,"dblUnderLine":false,"strikethrough":false,"script":3,"color":"#000","bgColor":"#ffffff"},"valid":0,"valid_content":{"require":true,"type":"string","phone_type":"","rule":{"max_length":20,"min_length":5}},"children":[{"type":"text","value":"","font_id":"font-742b32b0"},{"type":"text","value":"","font_id":"font-742b32b0"}],"name":"B0302","show_symbol":1},{"type":"text","value":"居住地址:","font_id":"font-543a65e8"},{"type":"field","id":"field-cde2a021","field_type":"normal","start_symbol":"[","end_symbol":"]","readonly":0,"deletable":1,"placeholder":"住址","tip":"","source_id":"","source_list":[],"show_format":0,"replace_format":0,"meta":{},"active_type":0,"multi_select":0,"max_width":0,"min_width":0,"style":{"id":"font-736d2409","height":19,"family":"宋体","bold":false,"italic":false,"underline":false,"dblUnderLine":false,"strikethrough":false,"script":3,"color":"#000","bgColor":"#ffffff"},"valid":0,"valid_content":{"require":true,"type":"string","phone_type":"","rule":{"max_length":20,"min_length":5}},"children":[{"type":"text","value":"","font_id":"font-742b32b0"},{"type":"text","value":"","font_id":"font-742b32b0"}],"name":"B0811","show_symbol":1},{"type":"text","value":"电话号码:","font_id":"font-543a65e8"},{"type":"field","id":"field-3545bf6d","field_type":"normal","start_symbol":"[","end_symbol":"]","readonly":0,"deletable":1,"placeholder":"电话","tip":"","source_id":"","source_list":[],"show_format":0,"replace_format":0,"meta":{},"active_type":0,"multi_select":0,"max_width":0,"min_width":0,"style":{"id":"font-3a0cf841","height":19,"family":"宋体","bold":false,"italic":false,"underline":false,"dblUnderLine":false,"strikethrough":false,"script":3,"color":"#000","bgColor":"#ffffff"},"valid":0,"valid_content":{"require":1,"type":"string","phone_type":"","rule":{"max_length":20,"min_length":5}},"children":[{"type":"text","value":"","font_id":"font-742b32b0"},{"type":"text","value":"","font_id":"font-742b32b0"}],"name":"B0828","show_symbol":1}],"title_length":0,"content_padding_left":0,"vertical_align":"top","level":0}],"groups":[],"fontMap":{"font-aea83bfb":{"id":"font-aea83bfb","height":16,"family":"宋体","bold":false,"italic":false,"underline":false,"dblUnderLine":false,"strikethrough":false,"script":3,"color":"#000","bgColor":null},"font-543a65e8":{"id":"font-543a65e8","height":19,"family":"宋体","bold":false,"italic":false,"underline":false,"dblUnderLine":false,"strikethrough":false,"script":3,"color":"#000","bgColor":"#ffffff"},"font-742b32b0":{"id":"font-742b32b0","height":19,"family":"宋体","bold":false,"italic":false,"underline":false,"dblUnderLine":false,"strikethrough":false,"script":3,"color":"blue","bgColor":"#ffffff"}},"bodyText":"","config":{"direction":"vertical","header_horizontal":true,"footer_horizontal":false},"meta":{}}`;
            const fields = editor.getFieldsByName("name");
            fields.forEach((field) => {
              editor.updateFieldRaw(field, rawData);
            });
          }
        },
        {
          title: "替换图片到页脚文本域",
          exe (e) {
            e.preventDefault();
            // 必须在获取文本域对象前进入页脚编辑模式（如果是替换页眉中文本域图片可不传或传入【header】）
            editor.enterEditHeaderAndFooterMode("footer");
            // 获取到页脚中name为【image】的文本域对象数组
            const fields = editor.getFieldsByName("name", editor.footer_cell);
            // 图片信息
            const image_info = {
              src: img_src,
              width: 150,
              height: 60
            };
            // 替换图片到文本域
            editor.replaceFieldsImage(fields, image_info);
            // 推出页眉页脚编辑模式
            editor.quitEditHeaderAndFooterMode();
          }
        },
        {
          title: "替换name属性的文本域内容为元素",
          exe (e) {
            e.preventDefault();
            const widget = editor.createElement("widget");
            const field = editor.createElement("field");
            const image = editor.createElement("image", { src: img_src, width: 200, height: 20, meta: { dsagsa: "我自定义的数据" } });
            const elements = ["11111", image, widget, field, "测试"];
            const fields = editor.getFieldsByName("name");
            fields.forEach((field) => {
              editor.updateFieldElements(field, elements);
            });
          }
        }, {
          title: "设置当前文本域最大宽度200",
          exe (e) {
            e.preventDefault();
            const field = editor.selection.getFocusField();
            if (field) {
              field.max_width = 200;
            }
          }
        }, {
          title: "设置当前文本域最小宽度100",
          exe (e) {
            e.preventDefault();
            const field = editor.selection.getFocusField();
            if (field) {
              field.min_width = 100;
            }
          }
        },
        {
          title: "显示隐藏文本域边框",
          exe (e) {
            e.preventDefault();
            editor.showFieldSymbol();
          }
        },
        {
          title: "设置文本域只读",
          exe (e) {
            e.preventDefault();
            editor.setFieldReadonly(true);
          }
        },
        {
          title: "清空文本域",
          exe (e) {
            e.preventDefault();
            const fields = editor.getAllFields(editor.root_cell);
            editor.clearFields(fields);
          }
        },
        {
          title: "设置文本域不可删除",
          exe (e) {
            e.preventDefault();
            editor.reviseFieldAttr({ deletable: 0 });
          }
        }
      ]
    },
    {
      title: "分组",
      children: [
        {
          title: "插入分组",
          exe (e) {
            e.preventDefault();
            editor.insertGroup(3, new Date().valueOf(), "test");
          }
        },
        {
          title: "上方插入分组",
          exe (e) {
            e.preventDefault();
            editor.insertGroup(2);
          }
        },
        {
          title: "下方插入分组",
          exe (e) {
            e.preventDefault();
            editor.insertGroup(3);
          }
        },
        {
          title: "删除分组",
          exe (e) {
            e.preventDefault();
            editor.deleteGroup();
          }
        },
        {
          title: "清空分组",
          exe (e) {
            e.preventDefault();
            const group = editor.selection.getFocusGroup();
            if (group) {
              editor.clearGroups([group]);
            }
          }
        },
        {
          title: "分组排序",
          exe (e) {
            e.preventDefault();
            editor.sortGroup();
          }
        },
        {
          title: "锁定/解锁",
          exe (e) {
            e.preventDefault();
            editor.setGroupIslock();
          }
        },
        {
          title: "复制分组",
          exe (e) {
            e.preventDefault();
            editor.copyGroup();
          }
        },
        {
          title: "分组换页",
          exe (e) {
            e.preventDefault();
            editor.groupToNextPage();
          }
        },
        {
          title: "打印分组文本域",
          exe (e) {
            e.preventDefault();
            const group = editor.selection.getFocusGroup();
            console.log(group.getGroupFields());
          }
        },
        {
          title: "分组表单",
          exe (e) {
            e.preventDefault();
            const group = editor.selection.getFocusGroup();
            if (group) {
              editor.setGroupFormMode(true, group);
            }
          }
        }
      ]
    },
    {
      title: "水印与图形",
      children: [
        { // 改变字体样式
          title: "改变字体样式",
          exe () {
            editor.changeMarkFontStyle({ height: 35 });
          }
        },
        { // 改变字体样式
          title: "改变字体样式",
          exe () {
            editor.changeMarkFontStyle({ color: "red" });
          }
        },
        { // 水印模式
          title: "水印模式",
          exe () {
            this.openWaterMark = !this.openWaterMark;
            editor.waterMarkModel(this.openWaterMark);
          }
        },
        { // 插入水印图片
          title: "插入水印图片",
          exe () {
            const params = {
              location: [0, 0],
              width: 400,
              height: 300
            };
            console.log(params);
            editor.insertWaterMarkImage(img_src, params);
          }
        },
        { // 改变重复模式
          title: "改变重复模式",
          exe () {
            this.markmode = !this.markmode;
            if (this.markmode) {
              editor.changeMarkMode("single");
            } else {
              editor.changeMarkMode("repeat");
            }
          }
        },
        { // 插入水印文字
          title: "插入水印文字",
          exe () {
            this.is_insert_text = !this.is_insert_text;
            editor.insertWaterMarkTextMode(this.is_insert_text);
          }
        },
        { // 开启/关闭形状模式
          title: "开启/关闭图形模式",
          exe () {
            this.open_shape = !this.open_shape;
            editor.shapeMode(this.open_shape);
          }

        }, {
          title: "开关插入线",
          exe (e) {
            e.preventDefault();
            editor.internal.drawShape("line");
          }
        },
        {
          title: "开关插入圆",

          exe (e) {
            e.preventDefault();
            editor.internal.drawShape("circle");
          }
        },
        {
          title: "开关插入矩形",

          exe (e) {
            e.preventDefault();
            editor.internal.drawShape("rect");
          }
        },
        {
          title: "开关插入折线",
          exe (e) {
            e.preventDefault();
            editor.internal.drawShape("fold_line");
          }
        },
        {
          title: "继续绘制折线",
          exe (e) {
            e.preventDefault();
            editor.internal.drawShape("continue_line");
          }
        },
        {
          title: "开关插入叉",
          exe (e) {
            e.preventDefault();
            editor.internal.drawShape("cross");
          }
        },
        {
          title: "关闭画线/叉/圆/折",
          exe (e) {
            e.preventDefault();
            editor.internal.drawShape("close");
          }
        }
      ]
    },
    {
      title: "批注",
      children: [
        {
          title: "添加批注", // 添加批注
          exe (e) {
            e.preventDefault();
            editor.internal.toggleCommentMode(true);

            editor.addComment({
              date: Date.now(),
              value: "添加一个随机批注",
              name: "李白"
            });
          }
        },
        {
          title: "删除批注",
          exe (e) {
            e.preventDefault();
            editor.uncomments();
            editor.render();
          }
        },
        {
          title: "更新批注内容",
          exe (e) {
            e.preventDefault();
            editor.updateComments("comment-c98e8be0$$null$$11-18", "更新后的内容");
          }
        },
        {
          title: "定位批注", // 定位到批注
          exe (e) {
            e.preventDefault();
            // editor.addHightLighterByCommentID("comment-ac545866$$document");
            // editor.addHightLighterByCommentID("comment-1e562c7d$$document");
            editor.addHightLighterByCommentID("comment-8ffc6653$$document");
          }
        },
        {
          title: "批注模式切换",
          exe (e) {
            e.preventDefault();
            editor.internal.toggleCommentMode(!editor.is_comment_mode);
          }
        }
      ]
    },
    {
      title: "打印",
      children: [
        {
          title: "系统打印奇数页",
          exe (e) {
            e.preventDefault();
            editor.printOddOrEven("odd");
          }
        }, {
          title: "系统打印偶数页",
          exe (e) {
            e.preventDefault();

            editor.printOddOrEven("even");
          }
        }, {

          title: "系统打印",
          exe (e) {
            e.preventDefault();
            const printParams = {
              printRatio: 1,
              printView: true
            };
            const printSrcList = editor.print(
              printParams
            );
            console.log(printSrcList);
          }
        }, {
          title: "续打选择",
          exe (e) {
            e.preventDefault();
            editor.print_continue = !editor.print_continue;
            editor.printContinue(editor.print_continue);
          }
        }, {
          title: "直接续打",
          exe (e) {
            e.preventDefault();
            editor.printContinueImmediate();
          }
        },
        {
          title: "批量打印",
          exe (e) {
            e.preventDefault();
            const rawStrings = [
              '{"header":[{"id":"para-04a55d2b","type":"p","align":"center","deepNum":0,"isOrder":false,"indentation":0,"before_paragraph_spacing":0,"row_ratio":1.4,"children":[{"type":"text","value":"","font_id":"font-a961296c"}],"content_padding_left":0}],"footer":[{"id":"para-448b00f2","type":"p","align":"center","deepNum":0,"isOrder":false,"indentation":0,"before_paragraph_spacing":0,"row_ratio":1.4,"children":[{"type":"text","value":"","font_id":"font-a961296c"}],"content_padding_left":0}],"content":[{"id":"para-10742a0b","type":"p","align":"left","deepNum":0,"isOrder":false,"indentation":0,"before_paragraph_spacing":0,"row_ratio":1.4,"children":[{"type":"text","value":"这3个文档","font_id":"font-a961296c"}],"content_padding_left":0}],"groups":[],"fontMap":{"font-a961296c":{"id":"font-a961296c","height":16,"family":"仿宋","bold":false,"italic":false,"underline":false,"dblUnderLine":false,"strikethrough":false,"script":3,"color":"#000","bgColor":null},"font-default-1480d1ea":{"id":"font-default-1480d1ea","height":16,"family":"宋体","bold":false,"italic":false,"underline":false,"dblUnderLine":false,"strikethrough":false,"script":3,"color":"#000","bgColor":null},"font-da0fe94b":{"id":"font-da0fe94b","height":16,"family":"宋体","bold":false,"italic":false,"underline":false,"dblUnderLine":false,"strikethrough":false,"script":3,"color":"blue","bgColor":null}},"bodyText":"","config":{"direction":"vertical","header_horizontal":true,"footer_horizontal":false},"meta":{}}',
              '{"header":[{"id":"para-04a55d2b","type":"p","align":"center","deepNum":0,"isOrder":false,"indentation":0,"before_paragraph_spacing":0,"row_ratio":1.4,"children":[{"type":"text","value":"","font_id":"font-a961296c"}],"content_padding_left":0}],"footer":[{"id":"para-448b00f2","type":"p","align":"center","deepNum":0,"isOrder":false,"indentation":0,"before_paragraph_spacing":0,"row_ratio":1.4,"children":[{"type":"text","value":"","font_id":"font-a961296c"}],"content_padding_left":0}],"content":[{"id":"para-10742a0b","type":"p","align":"left","deepNum":0,"isOrder":false,"indentation":0,"before_paragraph_spacing":0,"row_ratio":1.4,"children":[{"type":"text","value":"这是德尔个文档","font_id":"font-a961296c"}],"content_padding_left":0}],"groups":[],"fontMap":{"font-a961296c":{"id":"font-a961296c","height":16,"family":"仿宋","bold":false,"italic":false,"underline":false,"dblUnderLine":false,"strikethrough":false,"script":3,"color":"#000","bgColor":null},"font-default-1480d1ea":{"id":"font-default-1480d1ea","height":16,"family":"宋体","bold":false,"italic":false,"underline":false,"dblUnderLine":false,"strikethrough":false,"script":3,"color":"#000","bgColor":null},"font-da0fe94b":{"id":"font-da0fe94b","height":16,"family":"宋体","bold":false,"italic":false,"underline":false,"dblUnderLine":false,"strikethrough":false,"script":3,"color":"blue","bgColor":null}},"bodyText":"","config":{"direction":"vertical","header_horizontal":true,"footer_horizontal":false},"meta":{}}'
            ];

            const batchPrintList = rawStrings.map(item => JSON.parse(item));
            console.log("rawData批量转换为图片:", editor.rawData2Base64(batchPrintList));
          }
        }, {
          title: "区域打印",
          exe (e) {
            e.preventDefault();
            editor.area_print = !editor.area_print;
            editor.areaPrint(editor.area_print);
          }
        }
      ]
    },
    {
      title: "表格",
      children: [
        { // 设置表格在表单模式下可编辑
          title: "设置表格在表单模式下可编辑",
          exe () {
            this.set_tables_editable = !this.set_tables_editable;
            editor.setTablesEditableInFormMode(this.set_tables_editable);
          }

        },
        { // 设置表格在表单模式下可编辑
          title: "一键解锁",
          exe () {
            editor.unlockEverything();
          }

        },
        { // 固定单元格高度
          title: "固定单元格高度",
          exe () {
            editor.fixedCellHeight("scroll");
          }

        },
        { // 获取光标位置处字符
          title: "获取光标位置处字符",
          exe () {
            console.log(editor.getCharactersAtCaret());
          }

        },
        { // 给表格追加行和列
          title: "表格追加行和列",
          exe () {
            editor.insertRowAndColByName("testName", 1, 0);
          }
        },
        { // 表格指定行转原始数据
          title: "表格指定行转原始数据",
          exe () {
            const table = editor.root_cell.paragraph.find(c => c.row_size);
            if (table) {
              const rawData = editor.assignTableRow2RawData(table, [0, 2]);
              editor.insertTemplateData(rawData);
            } else {
              alert("未获取到表格！");
            }
          }
        },
        { // 固定表头
          title: "固定表头",
          exe () {
            editor.setFixedTableHeader();
            // editor.insertImageTable({ src: img_src, serialNum: i++, field: field, width: 300, height: 150 });
          }
        },
        { // 取消固定表头
          title: "取消固定表头",
          exe () {
            editor.setFixedTableHeader({ fixed_table_header_num: 0 });
            // editor.insertImageTable({ src: img_src, serialNum: i++, field: field, width: 300, height: 150 });
          }
        },
        { // 锁定表格
          title: "锁定表格",
          exe () {
            editor.setTablesLock();
          }
        },
        { // 取消锁定表格
          title: "取消锁定表格",
          exe () {
            editor.setTablesLock({ isLock: false });
          }
        },
        { // 测试禁止删除该表格
          title: "测试禁止删除该表格",
          exe () {
            editor.setTblIsCanDelete(true);
          }
        },
        { // 插入图片并排版
          title: "插入图片并排版",
          exe () {
            const srcs = ["https://img1.baidu.com/it/u=1394459067,3018342203&fm=253&fmt=auto&app=120&f=JPEG?w=353&h=500", "https://view-cache.book118.com/view3/M01/29/07/wKh2BF2nVC6AFjuyAAAYi5A8DEM243.png", "https://img0.baidu.com/it/u=1944966311,750301424&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500", "https://img0.baidu.com/it/u=405086877,2666710572&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500", "https://view-cache.book118.com/view3/M04/0A/20/wKh2BV2nVC6AW4GkAAAYeJykwPU260.png"];
            srcs.forEach((src, index) => {
              const field = editor.createElement("field");
              field.type = "select";
              field.source_list = [
                {
                  code: "检索1",
                  text: "选项1"
                },
                {
                  code: "检索2",
                  text: "选项2"
                },
                {
                  code: "检索3",
                  text: "选项3"
                },
                {
                  code: "检索4",
                  text: "选项4"
                },
                {
                  code: "检索5",
                  text: "选项5"
                }
              ];
              editor.insertImageTable({ src: src, width: 10, height: 8, field, maxTableHeight: 300, id: index + 1 });
            });
          }

        },
        { // 测试解禁删除该表格
          title: "test_can_del_tbl",
          exe () {
            editor.setTblIsCanDelete(false);
          }
        },
        {
          title: "split_table",
          exe (e) {
            e.preventDefault();
            editor.splitCell();
          }
        }, {
          title: "获取光标位置处的表格",
          exe (e) {
            e.preventDefault();
            editor.selection.getFocusTable();
          }
        }, {
          title: "合并单元格",
          exe (e) {
            e.preventDefault();
            editor.mergeCell();
          }
        }, {
          title: "显示隐藏表格线",
          exe (e) {
            e.preventDefault();
            editor.cotrolTableLine("all_line");
          }
        }, {
          title: "删除表格",
          exe (e) {
            e.preventDefault();
            editor.deleteTbl();
          }
        }, {
          title: "上方插入一行",
          exe (e) {
            e.preventDefault();
            editor.addRowOrColInTbl(Editor.builtInVariable.Direction.up);
          }
        }, {
          title: "上方插入一行",
          exe (e) {
            e.preventDefault();
            editor.addRowOrColInTbl(Editor.builtInVariable.Direction.down, "all");
          }
        }, {
          title: "左侧插入一列",
          exe (e) {
            e.preventDefault();
            editor.addRowOrColInTbl(Editor.builtInVariable.Direction.left);
          }
        }, {
          title: "右侧插入一列",
          exe (e) {
            e.preventDefault();
            editor.addRowOrColInTbl(Editor.builtInVariable.Direction.right);
          }
        }, {
          title: "斜上",
          exe (e) {
            e.preventDefault();
            editor.cotrolTableLine("inside_inclined_top_line");
          }
        },
        {
          title: "斜下",
          exe (e) {
            e.preventDefault();
            editor.cotrolTableLine("inside_inclined_bottom_line");
          }
        },
        {
          title: "删除表格上方空行",
          exe (e) {
            e.preventDefault();
            editor.deleteEmtptyParagraphOnTblSide(Editor.builtInVariable.Direction.up);
          }
        },
        {
          title: "删除表格下方空行",
          exe (e) {
            e.preventDefault();
            editor.deleteEmtptyParagraphOnTblSide(Editor.builtInVariable.Direction.down);
          }
        },
        {
          title: "表格上方插入空行",
          exe (e) {
            e.preventDefault();
            editor.insertEmptyParagraphOnTblSide(Editor.builtInVariable.Direction.up);
          }
        },
        {
          title: "表格下方插入空行",
          exe (e) {
            e.preventDefault();
            editor.insertEmptyParagraphOnTblSide(Editor.builtInVariable.Direction.down);
          }
        },
        {
          title: "显示上边线",
          exe (e) {
            e.preventDefault();
            editor.cotrolTableLine("top_line");
          }
        }, {
          title: "隐藏所有边线",
          exe (e) {
            e.preventDefault();
            editor.cotrolTableLine("no_line");
          }
        }, {
          title: "显示右边线",
          exe (e) {
            e.preventDefault();
            editor.cotrolTableLine("right_line");
          }
        }, {
          title: "显示隐藏外边线",
          exe (e) {
            e.preventDefault();
            editor.cotrolTableLine("out_line");
          }
        }, {
          title: "显示下边线",
          exe (e) {
            e.preventDefault();
            editor.cotrolTableLine("under_line");
          }
        }, {
          title: "隐藏显示内边线",
          exe (e) {
            e.preventDefault();
            editor.cotrolTableLine("inside_line");
          }
        }, {
          title: "显示左边线",
          exe (e) {
            e.preventDefault();
            editor.cotrolTableLine("left_line");
          }
        },
        {
          title: "显示隐藏内横框线",
          exe (e) {
            e.preventDefault();
            editor.cotrolTableLine("inside_flat_line");
          }
        },
        {
          title: "显示隐藏内竖框线",
          exe (e) {
            e.preventDefault();
            editor.cotrolTableLine("inside_vertical_line");
          }
        },
        {
          title: "段落字符串",
          exe (e) {
            e.preventDefault();
            alert(editor.getParaStr());
          }
        },
        {
          title: "表格内字符串",
          exe (e) {
            e.preventDefault();
            alert(editor.getTblStr());
          }
        },
        {
          title: "根据name获取表格",
          exe (e) {
            e.preventDefault();
            // const table = editor.selection.getFocusTable();
            // table.name = "testTable";
            const tables = editor.getTablesByName("pacs-image-table");
            console.log(tables);
            // tables[0].children.forEach((cell) => {
            //   cell.appendText("666");
            // });
            // editor.update();
            // editor.render();
          }
        },
        {
          title: "根据位置获取单元格",
          exe (e) {
            e.preventDefault();
            const table = editor.selection.getFocusTable();
            const cell = table.getCellByPosition(2, 1);
            console.log(cell, cell.getStr());
          }
        },
        {
          title: "单元格边距扩大",
          exe (e) {
            e.preventDefault();
            editor.setCellPadding(1, { left: 10, top: 10 });
          }
        },
        {
          title: "单元格边距减小",
          exe (e) {
            e.preventDefault();
            editor.setCellPadding(0, { left: 0, top: 0, right: 0 });
          }
        },
        {
          title: "插入表格",
          input: "prompt",
          exe (e, args) {
            e.preventDefault();
            editor.insertTable(Number(args[0]), Number(args[1]));
          }
        },
        {
          title: "修改表格的name值为",
          input: "prompt",
          exe (e, args) {
            e.preventDefault();
            const table = editor.selection.getFocusTable();
            table.name = args[0];
          }
        },
        {
          title: "删除整行",
          exe (e) {
            e.preventDefault();
            editor.deleteRowFromTbl();
          }
        }, {
          title: "删除整列",
          exe (e) {
            e.preventDefault();
            editor.deleteColFromTbl();
          }
        }, {
          title: "改变表格背景颜色",

          exe (e) {
            e.preventDefault();
            editor.changeTableBgStyle("red");
          }
        }, {
          title: "拆分单元格",

          exe (e) {
            e.preventDefault();
            if (editor.judgeIsCanSplitCell()) {
              editor.splitCell();
            }
          }
        }
      ]
    },
    {
      title: "其他",
      children: [
        { // 监听粘贴事件
          title: "监听粘贴事件",
          exe () {
            editor.event.on("beforePaste", ({ rawDataContent, fontStr }) => {
              const editor2 = editor.copyEditor();
              editor2.reInitRawCarryFont(rawDataContent, fontStr);
              const rawData = editor2.getRawData();
              return rawData.content;
            });
          }
        },
        { // navigate_to_document_end
          title: "导航到文档尾",
          exe () {
            // editor.selection.setCursorByRootCell("end");
            editor.toDocBottomResult();
            // editor.update();
            // editor.render();
          }
        },
        { // 护眼模式
          title: "护眼模式",
          exe () {
            this.eyeProtectionMode = !this.eyeProtectionMode;
            editor.eyeProtectionMode(this.eyeProtectionMode);
          }
        },
        { // 定位头部
          title: "定位到头部",
          exe () {
            editor.toDocTopResult();
          }
        },
        {
          title: "插入box",
          exe (e) {
            e.preventDefault();
            editor.insertBox({ box: "aaaaaaaaa" });
          }
        },
        {
          title: "撤销",
          exe (e) {
            e.preventDefault();
            Editor.history.undo(editor);
          }
        },
        {
          title: "重做",
          exe (e) {
            e.preventDefault();
            Editor.history.redo(editor);
          }
        },
        {
          title: "清空堆栈",
          exe (e) {
            e.preventDefault();
            Editor.history.clear();
          }
        }, {
          title: "插入图片",
          exe (e) {
            e.preventDefault();
            editor.insertImage(img_src, { width: 632, height: 469 });
          }
        },
        {
          title: "删除行",

          exe (e) {
            e.preventDefault();
            editor.deleteRowWithCaret();
          }
        },
        {
          title: "删除行(文本域)",

          exe (e) {
            e.preventDefault();
            editor.deleteRowWithCaret(editor.selection.getFocusField());
          }
        },
        {
          title: "send_file_pic",

          exe (e) {
            e.preventDefault();
            editor.getAllBlobs().then(blobs => {
              console.log("blobs", blobs);
            });
          }
        },
        {
          title: "是否展示页眉页脚",

          exe (e) {
            e.preventDefault();
            editor.isShowFooterOrHeader();
          }
        }, {
          title: "退出编辑页眉页脚",

          exe (e) {
            e.preventDefault();
            editor.exitEditHfMode();
          }
        }, {
          title: "禁止双击编辑页眉页脚",

          exe (e) {
            e.preventDefault();
            editor.is_forbid_edit_hf = !editor.is_forbid_edit_hf;
          }
        }, {
          title: "禁止双击编辑页眉页脚",

          exe (e) {
            e.preventDefault();
            editor.headerFooterHorizontal(false, true);
          }
        },
        {
          title: "只读模式切换",

          exe (e) {
            e.preventDefault();
            editor.setReadonly(!editor.readonly);
          }
        }
      ]
    }
  ];
}
